/**
 * Currency Configuration for the Platform
 * Sets ZAR (South African Rand) as the base currency
 */

export const CURRENCY_CONFIG = {
  // Base currency for the platform
  BASE_CURRENCY: 'ZAR',
  DEFAULT_CURRENCY: 'ZAR',
  
  // Currency symbols and formatting
  CURRENCY_SYMBOLS: {
    ZAR: 'R',
    USD: '$',
    EUR: '€',
    GBP: '£',
    CAD: 'C$',
    AUD: 'A$',
    JPY: '¥',
  },
  
  // Currency names
  CURRENCY_NAMES: {
    ZAR: 'South African Rand',
    USD: 'US Dollar',
    EUR: 'Euro',
    GBP: 'British Pound',
    CAD: 'Canadian Dollar',
    AUD: 'Australian Dollar',
    JPY: 'Japanese Yen',
  },
  
  // Default locale for formatting
  DEFAULT_LOCALE: 'en-ZA', // South African English
  
  // Regional settings
  DEFAULT_REGION: 'South Africa',
  DEFAULT_COUNTRY: 'South Africa',
  DEFAULT_TIMEZONE: 'Africa/Johannesburg',
} as const;

/**
 * Format currency amount with proper ZAR formatting
 */
export function formatCurrency(
  amount: number, 
  currency: string = CURRENCY_CONFIG.BASE_CURRENCY,
  locale: string = CURRENCY_CONFIG.DEFAULT_LOCALE
): string {
  if (currency === 'ZAR') {
    // South African Rand formatting: R1,234.56
    return `R${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
  }
  
  // Use Intl.NumberFormat for other currencies
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(amount);
  } catch (error) {
    // Fallback formatting
    const symbol = CURRENCY_CONFIG.CURRENCY_SYMBOLS[currency as keyof typeof CURRENCY_CONFIG.CURRENCY_SYMBOLS] || currency;
    return `${symbol}${amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
  }
}

/**
 * Parse currency string to number (handles ZAR 'R' symbol)
 */
export function parseCurrency(value: string | number): number {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    // Remove currency symbols and parse
    const cleaned = value.replace(/[R$£€¥C,\s]/g, '').trim();
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
}

/**
 * Get currency symbol for a given currency code
 */
export function getCurrencySymbol(currency: string): string {
  return CURRENCY_CONFIG.CURRENCY_SYMBOLS[currency as keyof typeof CURRENCY_CONFIG.CURRENCY_SYMBOLS] || currency;
}

/**
 * Get currency name for a given currency code
 */
export function getCurrencyName(currency: string): string {
  return CURRENCY_CONFIG.CURRENCY_NAMES[currency as keyof typeof CURRENCY_CONFIG.CURRENCY_NAMES] || currency;
}

/**
 * Get list of supported currencies for dropdowns
 */
export function getSupportedCurrencies(): Array<{ code: string; name: string; symbol: string }> {
  return Object.keys(CURRENCY_CONFIG.CURRENCY_NAMES).map(code => ({
    code,
    name: getCurrencyName(code),
    symbol: getCurrencySymbol(code),
  }));
}

/**
 * Platform defaults for forms and components
 */
export const PLATFORM_DEFAULTS = {
  currency: CURRENCY_CONFIG.BASE_CURRENCY,
  region: CURRENCY_CONFIG.DEFAULT_REGION,
  country: CURRENCY_CONFIG.DEFAULT_COUNTRY,
  timezone: CURRENCY_CONFIG.DEFAULT_TIMEZONE,
  locale: CURRENCY_CONFIG.DEFAULT_LOCALE,
} as const;
