#!/usr/bin/env node

/**
 * 🧪 MODULE IMPORT VERIFICATION SCRIPT
 * 
 * Tests that all imported modules are accessible and working
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 TESTING IMPORTED MODULES...');
console.log('='.repeat(50));

// Test file existence
function testFileExists(filePath, description) {
  const fullPath = path.join(__dirname, filePath);
  const exists = fs.existsSync(fullPath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  return exists;
}

// Test directory existence
function testDirExists(dirPath, description) {
  const fullPath = path.join(__dirname, dirPath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
  console.log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`);
  return exists;
}

let passCount = 0;
let totalTests = 0;

function runTest(testFn, ...args) {
  totalTests++;
  if (testFn(...args)) {
    passCount++;
  }
}

console.log('\n🔐 ADMIN MODULE TESTS');
console.log('-'.repeat(30));
runTest(testDirExists, 'src/components/admin', 'Admin Components Directory');
runTest(testFileExists, 'src/pages/AdminDashboard.tsx', 'Admin Dashboard Page');
runTest(testFileExists, 'src/pages/admin/UserManagement.tsx', 'User Management Page');
runTest(testFileExists, 'src/routes/adminRoutes.tsx', 'Admin Routes');
runTest(testFileExists, 'src/context/UserManagementContext.tsx', 'User Management Context');
runTest(testFileExists, 'src/utils/rbac/permissions.ts', 'RBAC Permissions');
runTest(testFileExists, 'src/types/rbac.ts', 'RBAC Types');

console.log('\n🛒 WOOCOMMERCE MODULE TESTS');
console.log('-'.repeat(30));
runTest(testDirExists, 'src/components/customers', 'Customer Components Directory');
runTest(testDirExists, 'src/components/customer-analytics', 'Customer Analytics Directory');
runTest(testFileExists, 'src/components/integrations/WooCommerceWrapper.tsx', 'WooCommerce Wrapper');
runTest(testFileExists, 'src/pages/customer-management/CustomerDirectoryPage.tsx', 'Customer Directory Page');
runTest(testFileExists, 'src/routes/customerManagementRoutes.tsx', 'Customer Management Routes');
runTest(testFileExists, 'src/hooks/useCustomerManagement.ts', 'Customer Management Hook');

console.log('\n🏢 ODOO MODULE TESTS');
console.log('-'.repeat(30));
runTest(testDirExists, 'src/components/financial', 'Financial Components Directory');
runTest(testDirExists, 'src/components/project-management', 'Project Management Directory');
runTest(testDirExists, 'src/components/suppliers', 'Suppliers Directory');
runTest(testFileExists, 'src/components/integrations/OdooIntegrationWrapper.tsx', 'Odoo Integration Wrapper');
runTest(testFileExists, 'src/routes/financialRoutes.tsx', 'Financial Routes');
runTest(testFileExists, 'src/routes/projectManagementRoutes.tsx', 'Project Management Routes');
runTest(testFileExists, 'src/services/financial/index.ts', 'Financial Services');
runTest(testFileExists, 'src/types/financial.ts', 'Financial Types');
runTest(testFileExists, 'src/types/project-management.ts', 'Project Management Types');

console.log('\n🔧 CORE INTEGRATION TESTS');
console.log('-'.repeat(30));
runTest(testFileExists, 'src/routes/AppRoutes.tsx', 'Main App Routes');
runTest(testFileExists, 'src/components/PermissionGuard.tsx', 'Permission Guard Component');
runTest(testFileExists, 'src/components/ProtectedRoute.tsx', 'Protected Route Component');

// Test route imports in AppRoutes.tsx
console.log('\n📋 ROUTE INTEGRATION TESTS');
console.log('-'.repeat(30));

try {
  const appRoutesContent = fs.readFileSync(path.join(__dirname, 'src/routes/AppRoutes.tsx'), 'utf8');
  
  const routeTests = [
    { import: 'AdminRoutes', route: '/admin/*', description: 'Admin Routes Import & Usage' },
    { import: 'CustomerManagementRoutes', route: '/customer-management/*', description: 'Customer Management Routes' },
    { import: 'FinancialRoutes', route: '/financial/*', description: 'Financial Routes' },
    { import: 'ProjectManagementRoutes', route: '/project-management/*', description: 'Project Management Routes' }
  ];
  
  routeTests.forEach(test => {
    totalTests++;
    const hasImport = appRoutesContent.includes(test.import);
    const hasRoute = appRoutesContent.includes(test.route);
    const passed = hasImport && hasRoute;
    
    console.log(`${passed ? '✅' : '❌'} ${test.description}`);
    if (passed) passCount++;
  });
  
} catch (error) {
  console.log('❌ Error reading AppRoutes.tsx:', error.message);
}

// Summary
console.log('\n📊 TEST SUMMARY');
console.log('='.repeat(50));
console.log(`✅ Passed: ${passCount}/${totalTests} tests`);
console.log(`❌ Failed: ${totalTests - passCount}/${totalTests} tests`);

const successRate = (passCount / totalTests * 100).toFixed(1);
console.log(`📈 Success Rate: ${successRate}%`);

if (passCount === totalTests) {
  console.log('\n🎉 ALL TESTS PASSED! Modules successfully imported and integrated!');
  console.log('🚀 Ready for development and testing!');
} else {
  console.log('\n⚠️  Some tests failed. Please review the missing files or configurations.');
}

console.log('\n🔗 Next Steps:');
console.log('1. Start the development server: npm run dev');
console.log('2. Test the admin dashboard: http://localhost:3001/admin');
console.log('3. Test customer management: http://localhost:3001/customer-management');
console.log('4. Test financial system: http://localhost:3001/financial');
console.log('5. Test project management: http://localhost:3001/project-management');

console.log('\n🎬 Testing complete!');
