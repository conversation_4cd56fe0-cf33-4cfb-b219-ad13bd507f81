#!/usr/bin/env node

/**
 * 🎬 IMMEDIATE MODULE EXTRACTION SCRIPT
 * 
 * LIGHTS, CAMERA, ACTION! 🚀
 * 
 * This script extracts all three modules RIGHT NOW from the current directory
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Helper functions to replace fs-extra
async function ensureDir(dirPath) {
  try {
    await fs.promises.mkdir(dirPath, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') throw error;
  }
}

async function pathExists(filePath) {
  try {
    await fs.promises.access(filePath);
    return true;
  } catch {
    return false;
  }
}

async function copy(src, dest) {
  const stat = await fs.promises.stat(src);
  if (stat.isDirectory()) {
    await ensureDir(dest);
    const files = await fs.promises.readdir(src);
    for (const file of files) {
      await copy(path.join(src, file), path.join(dest, file));
    }
  } else {
    await fs.promises.copyFile(src, dest);
  }
}

async function writeJson(filePath, data, options = {}) {
  const jsonString = JSON.stringify(data, null, options.spaces || 2);
  await fs.promises.writeFile(filePath, jsonString, 'utf8');
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const SOURCE_DIR = __dirname; // Current NXT-WEB-DEV-X directory
const DEST_BASE = path.resolve(__dirname, '../DOCUECHANGE/modules');

console.log('🎬🎬🎬 QUANTASORI MODULE EXTRACTION - LIGHTS, CAMERA, ACTION! 🎬🎬🎬');
console.log('='.repeat(80));
console.log(`📁 Source: ${SOURCE_DIR}`);
console.log(`📁 Destination: ${DEST_BASE}`);
console.log('='.repeat(80));

// Module 1: Admin and Systems Configuration
const ADMIN_MODULE = {
  name: 'admin-systems-config',
  files: [
    'src/components/admin',
    'src/pages/AdminDashboard.tsx',
    'src/pages/UserManagement.tsx',
    'src/auth',
    'src/components/auth',
    'src/components/PermissionGuard.tsx',
    'src/components/ProtectedRoute.tsx',
    'src/utils/rbac',
    'src/types/rbac.ts',
    'src/hooks/usePermissions.ts',
    'src/hooks/useUserPermissions.ts',
    'src/context/UserManagementContext.tsx',
    'src/routes/adminRoutes.tsx',
    'src/components/ui',
    'src/layouts/MainLayout.tsx',
    'src/layouts/ProtectedLayout.tsx',
    'src/lib/utils.ts',
    'src/integrations/supabase',
    'src/styles',
    'tailwind.config.js',
    'postcss.config.js',
    'tsconfig.json',
    'vite.config.ts',
    'components.json'
  ]
};

// Module 2: WooCommerce Integration
const WOOCOMMERCE_MODULE = {
  name: 'woocommerce-integration',
  files: [
    'src/components/integrations/woocommerce',
    'src/components/customers',
    'src/components/customer-analytics',
    'src/pages/customer-management',
    'src/pages/IntegrationsPage.tsx',
    'src/hooks/use-products.ts',
    'src/hooks/useCustomerManagement.ts',
    'src/types/product.ts',
    'src/routes/customerManagementRoutes.tsx',
    'src/components/ui',
    'src/lib/utils.ts',
    'src/integrations/supabase',
    'src/styles',
    'tailwind.config.js',
    'postcss.config.js',
    'tsconfig.json',
    'vite.config.ts',
    'components.json'
  ]
};

// Module 3: Odoo Integration
const ODOO_MODULE = {
  name: 'odoo-integration',
  files: [
    'src/components/integrations/odoo',
    'src/components/financial',
    'src/components/project-management',
    'src/components/suppliers',
    'src/pages/project-management',
    'src/pages/suppliers',
    'src/services/financial',
    'src/features/project-management',
    'src/types/financial.ts',
    'src/types/project-management.ts',
    'src/routes/projectManagementRoutes.tsx',
    'src/routes/financialRoutes.tsx',
    'src/layouts/ProjectManagementLayout.tsx',
    'src/components/ui',
    'src/lib/utils.ts',
    'src/integrations/supabase',
    'src/styles',
    'tailwind.config.js',
    'postcss.config.js',
    'tsconfig.json',
    'vite.config.ts',
    'components.json'
  ]
};

async function extractModule(module) {
  console.log(`\n🚀 Extracting ${module.name.toUpperCase()} module...`);
  console.log('-'.repeat(50));
  
  const moduleDir = path.join(DEST_BASE, module.name);
  await ensureDir(moduleDir);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const filePath of module.files) {
    try {
      const sourcePath = path.join(SOURCE_DIR, filePath);
      const destPath = path.join(moduleDir, filePath);
      
      if (await pathExists(sourcePath)) {
        await ensureDir(path.dirname(destPath));
        await copy(sourcePath, destPath);
        console.log(`✅ ${filePath}`);
        successCount++;
      } else {
        console.log(`⚠️  Not found: ${filePath}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${filePath} - ${error.message}`);
      errorCount++;
    }
  }
  
  // Create package.json for the module
  const packageJson = {
    name: `@quantasori/${module.name}`,
    version: '1.0.0',
    description: `QuantaSori ${module.name} module`,
    main: 'src/index.ts',
    type: 'module',
    scripts: {
      dev: 'vite',
      build: 'tsc && vite build',
      preview: 'vite preview'
    },
    dependencies: {
      '@supabase/supabase-js': '^2.39.0',
      '@tanstack/react-query': '^5.17.0',
      'react': '^18.2.0',
      'react-dom': '^18.2.0',
      'react-router-dom': '^6.20.1',
      'lucide-react': '^0.294.0',
      'clsx': '^2.0.0',
      'tailwind-merge': '^2.2.0',
      'sonner': '^1.3.1'
    },
    devDependencies: {
      '@types/react': '^18.2.43',
      '@types/react-dom': '^18.2.17',
      '@vitejs/plugin-react': '^4.2.1',
      'typescript': '^5.2.2',
      'vite': '^5.0.8'
    }
  };
  
  await writeJson(path.join(moduleDir, 'package.json'), packageJson, { spaces: 2 });

  // Create README
  const readme = `# ${module.name}

QuantaSori ${module.name} module extracted from main platform.

## Installation

\`\`\`bash
npm install
\`\`\`

## Development

\`\`\`bash
npm run dev
\`\`\`

## Build

\`\`\`bash
npm run build
\`\`\`
`;

  await fs.promises.writeFile(path.join(moduleDir, 'README.md'), readme);
  
  console.log(`📊 ${module.name}: ✅ ${successCount} files, ❌ ${errorCount} errors`);
  return { successCount, errorCount };
}

async function main() {
  try {
    // Ensure base modules directory exists
    await ensureDir(DEST_BASE);
    
    // Extract all modules
    const adminResult = await extractModule(ADMIN_MODULE);
    const wooResult = await extractModule(WOOCOMMERCE_MODULE);
    const odooResult = await extractModule(ODOO_MODULE);
    
    // Create master README
    const masterReadme = `# 🎬 QuantaSori Extracted Modules

LIGHTS, CAMERA, ACTION! 🚀

## Modules

- **admin-systems-config**: Admin dashboard and user management
- **woocommerce-integration**: E-commerce integration
- **odoo-integration**: ERP integration

## Extraction Results

- Admin: ✅ ${adminResult.successCount} files
- WooCommerce: ✅ ${wooResult.successCount} files  
- Odoo: ✅ ${odooResult.successCount} files

**Total**: ${adminResult.successCount + wooResult.successCount + odooResult.successCount} files extracted

Generated: ${new Date().toISOString()}
`;
    
    await fs.promises.writeFile(path.join(DEST_BASE, 'README.md'), masterReadme);
    
    console.log('\n🎉🎉🎉 MODULE EXTRACTION COMPLETE! 🎉🎉🎉');
    console.log('='.repeat(80));
    console.log(`📁 Modules extracted to: ${DEST_BASE}`);
    console.log(`📊 Total files: ${adminResult.successCount + wooResult.successCount + odooResult.successCount}`);
    console.log('🚀 Ready for independent development and deployment!');
    console.log('='.repeat(80));
    
  } catch (error) {
    console.error('💥 EXTRACTION FAILED:', error);
    process.exit(1);
  }
}

main();
