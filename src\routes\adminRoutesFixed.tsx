import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { PlatformLayout } from '@/components/layouts/PlatformLayout';

// Import existing admin components
const UserManagement = lazy(() => import('../pages/UserManagement'));
const Settings = lazy(() => import('../pages/Settings'));

// Import admin pages that exist - USE THE MAIN ADMIN DASHBOARD
const AdminDashboard = lazy(() => import('../pages/AdminDashboard')); // Main comprehensive dashboard
const AdminUserManagement = lazy(() => import('../pages/admin/UserManagement'));
const RolesPermissionsManager = lazy(() => import('../pages/admin/RolesPermissionsManager'));
const DocumentationPage = lazy(() => import('../pages/admin/DocumentationPage'));
const DatabaseAdminPage = lazy(() => import('../pages/admin/DatabaseAdminPage'));
const AdminModuleAccess = lazy(() => import('../pages/admin/AdminModuleAccess')); // Toggle Module Access
const AuditLogPage = lazy(() => import('../pages/admin/AuditLogPage'));

// Import other existing pages
const Unauthorized = lazy(() => import('../pages/Unauthorized'));

// Loading fallback
const AdminLoadingFallback = (): JSX.Element => (
  <div className="flex items-center justify-center h-full min-h-[400px]">
    <div className="text-center">
      <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
      <p className="text-gray-600">Loading Admin Module...</p>
    </div>
  </div>
);

// Placeholder component for missing admin features
const AdminComingSoonPlaceholder = ({ title }: { title: string }) => (
  <div className="flex items-center justify-center h-full min-h-[400px]">
    <div className="text-center">
      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <span className="text-2xl">⚙️</span>
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">{title}</h2>
      <p className="text-gray-600 mb-4">This admin feature is coming soon!</p>
      <p className="text-sm text-gray-500">We're working hard to bring you this functionality.</p>
    </div>
  </div>
);

// Simple permission guard placeholder
const PermissionGuard = ({ children, requiredPermissions }: { children: React.ReactNode; requiredPermissions?: string[] }) => {
  // For now, just render children - implement proper permission checking later
  return <>{children}</>;
};

/**
 * Fixed Admin Module Routes
 * Only imports existing components to prevent build errors
 */
export const AdminRoutesFixed: React.FC = () => {
  return (
    <Routes>
      {/* Index route - redirects to dashboard */}
      <Route index element={
        <PlatformLayout moduleTitle="Admin Dashboard">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminDashboard />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />

      {/* Dashboard route - explicit dashboard path */}
      <Route path="dashboard" element={
        <PlatformLayout moduleTitle="Admin Dashboard">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminDashboard />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      {/* Settings route - standardized settings pattern */}
      <Route path="settings" element={
        <PlatformLayout moduleTitle="Admin Settings">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <Settings />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      {/* User Management */}
      <Route path="users" element={
        <PlatformLayout moduleTitle="User Management">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminUserManagement />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      {/* Module Access - Toggle Module Access */}
      <Route path="module-access" element={
        <PlatformLayout moduleTitle="Toggle Module Access">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminModuleAccess />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />

      {/* Toggle Module Access - Alternative path */}
      <Route path="toggle-module-access" element={
        <PlatformLayout moduleTitle="Toggle Module Access">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminModuleAccess />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      {/* Documentation */}
      <Route path="documentation" element={
        <PlatformLayout moduleTitle="Admin Documentation">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <DocumentationPage />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      {/* Database Admin */}
      <Route path="database" element={
        <PlatformLayout moduleTitle="Database Admin">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <DatabaseAdminPage />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      {/* System Settings */}
      <Route path="system-settings" element={
        <PlatformLayout moduleTitle="System Settings">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <Settings />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />

      {/* Roles and Permissions Management */}
      <Route path="roles" element={
        <PlatformLayout moduleTitle="Roles & Permissions Management">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <RolesPermissionsManager />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />

      <Route path="permissions" element={
        <PlatformLayout moduleTitle="Roles & Permissions Management">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <RolesPermissionsManager />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      <Route path="security" element={
        <PlatformLayout moduleTitle="Security Center">
          <AdminComingSoonPlaceholder title="Security Center" />
        </PlatformLayout>
      } />
      
      <Route path="logs" element={
        <PlatformLayout moduleTitle="Activity Logs">
          <AdminComingSoonPlaceholder title="Activity Logs" />
        </PlatformLayout>
      } />
      
      <Route path="audit" element={
        <PlatformLayout moduleTitle="Audit Logs">
          <PermissionGuard requiredPermissions={["admin"]}>
            <Suspense fallback={<AdminLoadingFallback />}>
              <AuditLogPage />
            </Suspense>
          </PermissionGuard>
        </PlatformLayout>
      } />
      
      <Route path="backup" element={
        <PlatformLayout moduleTitle="Backup Management">
          <AdminComingSoonPlaceholder title="Backup Management" />
        </PlatformLayout>
      } />
      
      <Route path="monitoring" element={
        <PlatformLayout moduleTitle="System Monitoring">
          <AdminComingSoonPlaceholder title="System Monitoring" />
        </PlatformLayout>
      } />

      {/* Unauthorized route */}
      <Route path="unauthorized" element={
        <Suspense fallback={<AdminLoadingFallback />}>
          <Unauthorized />
        </Suspense>
      } />
      
      {/* Catch-all for unknown paths within /admin */}
      <Route path="*" element={<Navigate to="/dashboard/admin/dashboard" replace />} />
    </Routes>
  );
};

// Default export for React.lazy
export default AdminRoutesFixed;
