#!/usr/bin/env node

/**
 * WooCommerce Staging System Test Script
 * Tests database structure and API service functionality
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseStructure() {
  console.log('🔍 Testing database structure...');
  
  const requiredTables = [
    'woocommerce_config',
    'woocommerce_customers_staging',
    'woocommerce_products_staging',
    'woocommerce_orders_staging',
    'woocommerce_sync_log'
  ];

  for (const table of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Table ${table} error:`, error.message);
        return false;
      } else {
        console.log(`✅ Table ${table} exists and accessible`);
      }
    } catch (err) {
      console.error(`❌ Table ${table} test failed:`, err.message);
      return false;
    }
  }
  
  return true;
}

async function testWooCommerceConfig() {
  console.log('\n🔧 Testing WooCommerce configuration...');
  
  const testConfig = {
    organization_id: 'test-org-' + Date.now(),
    store_url: 'https://test-store.com',
    consumer_key: 'ck_test_key',
    consumer_secret: 'cs_test_secret',
    api_version: 'v3',
    sync_enabled: true,
    batch_size: 100,
    sync_frequency_minutes: 15
  };

  try {
    // Insert test config
    const { data: insertData, error: insertError } = await supabase
      .from('woocommerce_config')
      .insert(testConfig)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Failed to insert test config:', insertError.message);
      return false;
    }

    console.log('✅ WooCommerce config insert successful');

    // Update test config
    const { data: updateData, error: updateError } = await supabase
      .from('woocommerce_config')
      .update({ batch_size: 200 })
      .eq('id', insertData.id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Failed to update test config:', updateError.message);
      return false;
    }

    console.log('✅ WooCommerce config update successful');

    // Clean up test data
    await supabase
      .from('woocommerce_config')
      .delete()
      .eq('id', insertData.id);

    console.log('✅ Test config cleanup successful');
    return true;

  } catch (err) {
    console.error('❌ WooCommerce config test failed:', err.message);
    return false;
  }
}

async function testStagingTables() {
  console.log('\n📦 Testing staging tables...');
  
  // Test customer staging
  const testCustomer = {
    wc_customer_id: 12345,
    organization_id: 'test-org-' + Date.now(),
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'Customer',
    billing_country: 'ZA',
    raw_data: { test: 'data' },
    sync_status: 'pending'
  };

  try {
    const { data: customerData, error: customerError } = await supabase
      .from('woocommerce_customers_staging')
      .insert(testCustomer)
      .select()
      .single();

    if (customerError) {
      console.error('❌ Customer staging insert failed:', customerError.message);
      return false;
    }

    console.log('✅ Customer staging insert successful');

    // Test product staging
    const testProduct = {
      wc_product_id: 67890,
      organization_id: testCustomer.organization_id,
      name: 'Test Product',
      sku: 'TEST-SKU-001',
      price: 99.99,
      currency: 'ZAR',
      raw_data: { test: 'product data' },
      sync_status: 'pending'
    };

    const { data: productData, error: productError } = await supabase
      .from('woocommerce_products_staging')
      .insert(testProduct)
      .select()
      .single();

    if (productError) {
      console.error('❌ Product staging insert failed:', productError.message);
      return false;
    }

    console.log('✅ Product staging insert successful');

    // Test sync log
    const testSyncLog = {
      organization_id: testCustomer.organization_id,
      sync_type: 'customers',
      sync_mode: 'batch',
      batch_size: 100,
      records_processed: 1,
      records_success: 1,
      records_error: 0,
      records_skipped: 0,
      status: 'completed'
    };

    const { data: syncLogData, error: syncLogError } = await supabase
      .from('woocommerce_sync_log')
      .insert(testSyncLog)
      .select()
      .single();

    if (syncLogError) {
      console.error('❌ Sync log insert failed:', syncLogError.message);
      return false;
    }

    console.log('✅ Sync log insert successful');

    // Clean up test data
    await Promise.all([
      supabase.from('woocommerce_customers_staging').delete().eq('id', customerData.id),
      supabase.from('woocommerce_products_staging').delete().eq('id', productData.id),
      supabase.from('woocommerce_sync_log').delete().eq('id', syncLogData.id)
    ]);

    console.log('✅ Staging tables cleanup successful');
    return true;

  } catch (err) {
    console.error('❌ Staging tables test failed:', err.message);
    return false;
  }
}

async function testCurrencyDefaults() {
  console.log('\n💰 Testing ZAR currency defaults...');
  
  try {
    // Test that ZAR is properly handled in staging
    const testProduct = {
      wc_product_id: 99999,
      organization_id: 'test-currency-' + Date.now(),
      name: 'ZAR Test Product',
      price: 1299.99,
      currency: 'ZAR',
      raw_data: { currency: 'ZAR', price: 'R1,299.99' },
      sync_status: 'pending'
    };

    const { data, error } = await supabase
      .from('woocommerce_products_staging')
      .insert(testProduct)
      .select()
      .single();

    if (error) {
      console.error('❌ ZAR currency test failed:', error.message);
      return false;
    }

    if (data.currency === 'ZAR' && data.price === 1299.99) {
      console.log('✅ ZAR currency handling successful');
    } else {
      console.error('❌ ZAR currency values incorrect:', { currency: data.currency, price: data.price });
      return false;
    }

    // Clean up
    await supabase.from('woocommerce_products_staging').delete().eq('id', data.id);
    return true;

  } catch (err) {
    console.error('❌ Currency test failed:', err.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting WooCommerce Staging System Tests\n');
  
  const tests = [
    { name: 'Database Structure', fn: testDatabaseStructure },
    { name: 'WooCommerce Configuration', fn: testWooCommerceConfig },
    { name: 'Staging Tables', fn: testStagingTables },
    { name: 'Currency Defaults', fn: testCurrencyDefaults }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`\n✅ ${test.name} - PASSED`);
      } else {
        failed++;
        console.log(`\n❌ ${test.name} - FAILED`);
      }
    } catch (err) {
      failed++;
      console.log(`\n❌ ${test.name} - ERROR:`, err.message);
    }
  }

  console.log('\n' + '='.repeat(50));
  console.log('🎯 TEST RESULTS SUMMARY');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! WooCommerce staging system is ready for deployment.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(err => {
    console.error('❌ Test runner failed:', err);
    process.exit(1);
  });
}

module.exports = {
  testDatabaseStructure,
  testWooCommerceConfig,
  testStagingTables,
  testCurrencyDefaults,
  runAllTests
};
