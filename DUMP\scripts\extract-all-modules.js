#!/usr/bin/env node

/**
 * 🎬 MASTER MODULE EXTRACTION SCRIPT
 * 
 * This script orchestrates the extraction of all three modules:
 * 1. Admin and Systems Configuration
 * 2. WooCommerce Integration  
 * 3. Odoo Integration
 * 
 * LIGHTS, CAMERA, ACTION! 🎬🎬🎬
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import extraction modules
import extractAdminModule from './extract-admin-module.js';
import extractWooCommerceModule from './extract-woocommerce-module.js';
import extractOdooModule from './extract-odoo-module.js';

const MODULES_DIR = path.resolve(__dirname, '../modules');

async function createMasterStructure() {
  console.log('🎬🎬🎬 QUANTASORI MODULE EXTRACTION MASTER SCRIPT 🎬🎬🎬');
  console.log('='.repeat(80));
  console.log('🚀 LIGHTS, CAMERA, ACTION! Starting complete module extraction...');
  console.log('='.repeat(80));
  
  try {
    // Ensure modules directory exists
    await fs.ensureDir(MODULES_DIR);
    
    // Create master README
    const masterReadme = `# 🎬 QuantaSori Extracted Modules

This directory contains the extracted modules from the main QuantaSori platform.

## 📦 Available Modules

### 🛡️ Admin and Systems Configuration
- **Location**: \`./admin-systems-config/\`
- **Description**: Complete admin dashboard with RBAC, user management, and system configuration
- **Features**: User management, permissions, role-based access control, system settings

### 🛒 WooCommerce Integration
- **Location**: \`./woocommerce-integration/\`
- **Description**: Complete WooCommerce e-commerce integration with real-time sync capabilities
- **Features**: Product sync, customer management, order processing, inventory sync

### 🏢 Odoo Integration
- **Location**: \`./odoo-integration/\`
- **Description**: Complete Odoo ERP integration with business process automation
- **Features**: Financial management, HR, project management, supply chain, vendor management

## 🚀 Quick Start

Each module is independently deployable and can be used as a standalone package or integrated into existing applications.

### Installation

\`\`\`bash
# Install a specific module
cd admin-systems-config
npm install

# Or install all modules
npm run install:all
\`\`\`

### Development

\`\`\`bash
# Start development server for a module
cd admin-systems-config
npm run dev

# Or start all modules in development mode
npm run dev:all
\`\`\`

## 📁 Module Structure

Each module follows the same structure:
\`\`\`
module-name/
├── src/
│   ├── components/
│   ├── pages/
│   ├── hooks/
│   ├── services/
│   ├── types/
│   ├── utils/
│   └── index.ts
├── package.json
├── README.md
├── tsconfig.json
└── vite.config.ts
\`\`\`

## 🔧 Configuration

Each module includes its own configuration files and can be customized independently.

## 📄 License

MIT © QuantaSori Development Team

---

**Extracted on**: ${new Date().toISOString()}
**Extraction Tool**: QuantaSori Module Extractor v1.0.0
`;
    
    await fs.writeFile(path.join(MODULES_DIR, 'README.md'), masterReadme);
    console.log('✅ Created master modules README.md');
    
    // Create master package.json for module management
    const masterPackageJson = {
      name: '@quantasori/extracted-modules',
      version: '1.0.0',
      description: 'Extracted modules from QuantaSori platform',
      private: true,
      scripts: {
        'install:all': 'npm run install:admin && npm run install:woocommerce && npm run install:odoo',
        'install:admin': 'cd admin-systems-config && npm install',
        'install:woocommerce': 'cd woocommerce-integration && npm install',
        'install:odoo': 'cd odoo-integration && npm install',
        'dev:all': 'concurrently "npm run dev:admin" "npm run dev:woocommerce" "npm run dev:odoo"',
        'dev:admin': 'cd admin-systems-config && npm run dev',
        'dev:woocommerce': 'cd woocommerce-integration && npm run dev',
        'dev:odoo': 'cd odoo-integration && npm run dev',
        'build:all': 'npm run build:admin && npm run build:woocommerce && npm run build:odoo',
        'build:admin': 'cd admin-systems-config && npm run build',
        'build:woocommerce': 'cd woocommerce-integration && npm run build',
        'build:odoo': 'cd odoo-integration && npm run build',
        'test:all': 'npm run test:admin && npm run test:woocommerce && npm run test:odoo',
        'test:admin': 'cd admin-systems-config && npm run test',
        'test:woocommerce': 'cd woocommerce-integration && npm run test',
        'test:odoo': 'cd odoo-integration && npm run test'
      },
      devDependencies: {
        'concurrently': '^8.2.2'
      },
      workspaces: [
        'admin-systems-config',
        'woocommerce-integration',
        'odoo-integration'
      ],
      keywords: ['quantasori', 'modules', 'extraction', 'admin', 'woocommerce', 'odoo'],
      author: 'QuantaSori Development Team',
      license: 'MIT'
    };
    
    await fs.writeJson(path.join(MODULES_DIR, 'package.json'), masterPackageJson, { spaces: 2 });
    console.log('✅ Created master modules package.json');
    
    return true;
  } catch (error) {
    console.error('❌ Error creating master structure:', error);
    return false;
  }
}

async function runExtractions() {
  console.log('\n🎬 Starting module extractions...\n');
  
  const results = {
    admin: false,
    woocommerce: false,
    odoo: false
  };
  
  try {
    // Extract Admin Module
    console.log('🛡️  EXTRACTING ADMIN MODULE...');
    console.log('-'.repeat(50));
    results.admin = await extractAdminModule();
    console.log(results.admin ? '✅ Admin module extracted successfully!' : '❌ Admin module extraction failed!');
    
    console.log('\n🛒 EXTRACTING WOOCOMMERCE MODULE...');
    console.log('-'.repeat(50));
    results.woocommerce = await extractWooCommerceModule();
    console.log(results.woocommerce ? '✅ WooCommerce module extracted successfully!' : '❌ WooCommerce module extraction failed!');
    
    console.log('\n🏢 EXTRACTING ODOO MODULE...');
    console.log('-'.repeat(50));
    results.odoo = await extractOdooModule();
    console.log(results.odoo ? '✅ Odoo module extracted successfully!' : '❌ Odoo module extraction failed!');
    
  } catch (error) {
    console.error('💥 Error during extractions:', error);
  }
  
  return results;
}

async function generateExtractionReport(results) {
  console.log('\n📊 GENERATING EXTRACTION REPORT...');
  
  const report = {
    timestamp: new Date().toISOString(),
    results: results,
    summary: {
      total: 3,
      successful: Object.values(results).filter(Boolean).length,
      failed: Object.values(results).filter(r => !r).length
    }
  };
  
  const reportContent = `# 📊 Module Extraction Report

**Generated**: ${report.timestamp}

## 📈 Summary
- **Total Modules**: ${report.summary.total}
- **Successfully Extracted**: ${report.summary.successful}
- **Failed**: ${report.summary.failed}
- **Success Rate**: ${Math.round((report.summary.successful / report.summary.total) * 100)}%

## 📋 Detailed Results

### 🛡️ Admin and Systems Configuration
- **Status**: ${results.admin ? '✅ SUCCESS' : '❌ FAILED'}
- **Location**: \`./modules/admin-systems-config/\`

### 🛒 WooCommerce Integration
- **Status**: ${results.woocommerce ? '✅ SUCCESS' : '❌ FAILED'}
- **Location**: \`./modules/woocommerce-integration/\`

### 🏢 Odoo Integration
- **Status**: ${results.odoo ? '✅ SUCCESS' : '❌ FAILED'}
- **Location**: \`./modules/odoo-integration/\`

## 🚀 Next Steps

${report.summary.successful > 0 ? `
### For Successfully Extracted Modules:
1. Navigate to the module directory
2. Run \`npm install\` to install dependencies
3. Run \`npm run dev\` to start development server
4. Customize configuration as needed
` : ''}

${report.summary.failed > 0 ? `
### For Failed Extractions:
1. Check the console output for specific errors
2. Verify source file paths exist
3. Ensure proper permissions
4. Re-run the extraction script
` : ''}

---
**Extraction Tool**: QuantaSori Module Extractor v1.0.0
`;
  
  await fs.writeFile(path.join(MODULES_DIR, 'EXTRACTION_REPORT.md'), reportContent);
  console.log('✅ Generated extraction report');
  
  return report;
}

async function main() {
  console.log('🎬🎬🎬 QUANTASORI MASTER MODULE EXTRACTION 🎬🎬🎬');
  console.log('🚀🚀🚀 LIGHTS, CAMERA, ACTION! 🚀🚀🚀');
  console.log('='.repeat(80));
  
  const startTime = Date.now();
  
  try {
    // Step 1: Create master structure
    console.log('📁 Creating master module structure...');
    const structureCreated = await createMasterStructure();
    if (!structureCreated) {
      throw new Error('Failed to create master structure');
    }
    
    // Step 2: Run all extractions
    const results = await runExtractions();
    
    // Step 3: Generate report
    const report = await generateExtractionReport(results);
    
    // Final summary
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('\n🎉🎉🎉 MASTER EXTRACTION COMPLETE! 🎉🎉🎉');
    console.log('='.repeat(80));
    console.log(`⏱️  Total Duration: ${duration} seconds`);
    console.log(`📊 Success Rate: ${Math.round((report.summary.successful / report.summary.total) * 100)}%`);
    console.log(`📁 Modules Location: ${MODULES_DIR}`);
    console.log(`📄 Report: ${path.join(MODULES_DIR, 'EXTRACTION_REPORT.md')}`);
    console.log('🚀 All modules are ready for independent deployment!');
    console.log('='.repeat(80));
    
    if (report.summary.failed > 0) {
      console.log('⚠️  Some extractions failed. Check the report for details.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 MASTER EXTRACTION FAILED:', error.message);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
