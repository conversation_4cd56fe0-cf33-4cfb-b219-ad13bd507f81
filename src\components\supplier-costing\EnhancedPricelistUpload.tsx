import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Download,
  X,
  Plus,
  Eye
} from 'lucide-react';
import { useLocalPricelist } from '@/hooks/useLocalPricelist';
import { cn } from '@/lib/utils';
import { type ProcessedPricelistData } from '@/services/localPricelistService';

interface UploadResult {
  file_name: string;
  status: 'completed' | 'error';
  total_products?: number;
  error_message?: string;
  id?: string;
  supplier_name?: string;
}

interface EnhancedPricelistUploadProps {
  onUploadComplete?: (data: UploadResult[]) => void;
  className?: string;
}

export const EnhancedPricelistUpload: React.FC<EnhancedPricelistUploadProps> = ({
  onUploadComplete,
  className
}) => {
  const { uploadFile, uploading, error, suppliers } = useLocalPricelist();
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [selectedSupplier, setSelectedSupplier] = useState<string>('');
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files).filter(file => {
      const ext = file.name.split('.').pop()?.toLowerCase();
      return ['xlsx', 'xls', 'csv', 'json'].includes(ext || '');
    });
    
    setSelectedFiles(prev => [...prev, ...files]);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...files]);
    }
  }, []);

  const removeFile = useCallback((index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleUpload = useCallback(async () => {
    if (selectedFiles.length === 0) return;

    const results: UploadResult[] = [];
    for (const file of selectedFiles) {
      try {
        const result: ProcessedPricelistData = await uploadFile(file, selectedSupplier);
        const uploadResult: UploadResult = {
          file_name: result.file_name,
          status: result.status,
          total_products: result.total_products,
          id: result.id,
          supplier_name: result.supplier_name
        };
        results.push(uploadResult);
      } catch (err) {
        const errorResult: UploadResult = {
          file_name: file.name,
          status: 'error',
          error_message: err instanceof Error ? err.message : 'Upload failed'
        };
        results.push(errorResult);
      }
    }

    setUploadResults(results);
    setShowResults(true);
    setSelectedFiles([]);
    
    if (onUploadComplete) {
      onUploadComplete(results);
    }
  }, [selectedFiles, selectedSupplier, uploadFile, onUploadComplete]);

  const downloadTemplate = useCallback((format: 'xlsx' | 'csv') => {
    const headers = [
      'supplier_name',
      'company_code',
      'contact_email',
      'product_code',
      'product_name',
      'category',
      'brand',
      'unit_price',
      'currency',
      'minimum_order_qty',
      'lead_time_days',
      'discount_tier_1',
      'discount_tier_2',
      'discount_tier_3',
      'last_updated',
      'valid_until',
      'region',
      'terms'
    ];

    const sampleData = [
      'TechSource Global',
      'TSG',
      '<EMAIL>',
      'LAP-DL-7420',
      'Dell Latitude 7420 Laptop',
      'Computing Hardware',
      'Dell',
      'R23999.99',
      'ZAR',
      '5',
      '14',
      '0.05',
      '0.08',
      '0.12',
      '2025-05-20',
      '2025-08-20',
      'South Africa',
      'NET30'
    ];

    if (format === 'csv') {
      const csvContent = [
        headers.join(','),
        sampleData.join(',')
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'supplier_pricelist_template.csv';
      a.click();
      URL.revokeObjectURL(url);
    } else {
      // For XLSX, we'd need a library like xlsx to generate the file
      // For now, just download CSV
      downloadTemplate('csv');
    }
  }, []);

  return (
    <div className={cn('space-y-6', className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Enhanced Pricelist Upload
          </CardTitle>
          <CardDescription>
            Upload supplier pricelists in multiple formats (XLSX, CSV, JSON) with real-time validation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upload">Upload Files</TabsTrigger>
              <TabsTrigger value="template">Download Template</TabsTrigger>
              <TabsTrigger value="results" disabled={!showResults}>
                Results {uploadResults.length > 0 && `(${uploadResults.length})`}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              {/* Supplier Selection */}
              <div className="space-y-2">
                <Label htmlFor="supplier">Supplier (Optional)</Label>
                <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select existing supplier or leave blank for auto-detection" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map(supplier => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name} ({supplier.productCount} products)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* File Drop Zone */}
              <div
                className={cn(
                  'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
                  dragOver ? 'border-primary bg-primary/5' : 'border-gray-300',
                  'hover:border-primary hover:bg-primary/5'
                )}
                onDrop={handleDrop}
                onDragOver={(e) => {
                  e.preventDefault();
                  setDragOver(true);
                }}
                onDragLeave={() => setDragOver(false)}
              >
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    Drop files here or click to select
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports XLSX, XLS, CSV, and JSON files
                  </p>
                  <Input
                    type="file"
                    multiple
                    accept=".xlsx,.xls,.csv,.json"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload"
                  />
                  <Label htmlFor="file-upload">
                    <Button variant="outline" className="cursor-pointer" asChild>
                      <span>Select Files</span>
                    </Button>
                  </Label>
                </div>
              </div>

              {/* Selected Files */}
              {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected Files ({selectedFiles.length})</Label>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {selectedFiles.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded border"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">{file.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {(file.size / 1024).toFixed(1)} KB
                          </Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Upload Button */}
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  {selectedFiles.length} file(s) selected
                </div>
                <Button
                  onClick={handleUpload}
                  disabled={selectedFiles.length === 0 || uploading}
                  className="min-w-[120px]"
                >
                  {uploading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Upload Files
                    </>
                  )}
                </Button>
              </div>

              {/* Progress Bar */}
              {uploading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Processing files...</span>
                    <span>Please wait</span>
                  </div>
                  <Progress value={45} className="w-full" />
                </div>
              )}

              {/* Error Display */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="template" className="space-y-4">
              <div className="text-center space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Download Template Files</h3>
                  <p className="text-sm text-gray-500">
                    Use these templates to format your supplier data correctly
                  </p>
                </div>
                
                <div className="flex justify-center gap-4">
                  <Button
                    variant="outline"
                    onClick={() => downloadTemplate('csv')}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    CSV Template
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => downloadTemplate('xlsx')}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Excel Template
                  </Button>
                </div>

                <div className="bg-gray-50 rounded-lg p-4 text-left">
                  <h4 className="font-medium mb-2">Required Fields:</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>• supplier_name</div>
                    <div>• product_code</div>
                    <div>• product_name</div>
                    <div>• unit_price</div>
                    <div>• category</div>
                    <div>• currency</div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Other fields are optional but recommended for better analytics
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              {uploadResults.length > 0 && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Upload Results</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setUploadResults([]);
                        setShowResults(false);
                      }}
                    >
                      Clear Results
                    </Button>
                  </div>
                  
                  <div className="space-y-2">
                    {uploadResults.map((result, index) => (
                      <Card key={index} className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {result.status === 'completed' ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : (
                              <AlertCircle className="h-5 w-5 text-red-500" />
                            )}
                            <div>
                              <p className="font-medium">{result.file_name}</p>
                              {result.status === 'completed' && result.total_products && (
                                <p className="text-sm text-gray-500">
                                  {result.total_products} products processed
                                </p>
                              )}
                              {result.error_message && (
                                <p className="text-sm text-red-500">
                                  {result.error_message}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={result.status === 'completed' ? 'default' : 'destructive'}
                            >
                              {result.status}
                            </Badge>
                            {result.status === 'completed' && (
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
