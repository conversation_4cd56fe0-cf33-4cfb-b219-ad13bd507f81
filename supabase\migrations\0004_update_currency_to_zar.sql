-- Migration to update platform currency to ZAR (South African Rand)
-- This migration updates all currency-related defaults and existing data

-- Update marketing_settings default currency to ZAR
ALTER TABLE public.marketing_settings 
ALTER COLUMN default_currency SET DEFAULT 'ZAR';

-- Update any existing marketing_settings records to use ZAR
UPDATE public.marketing_settings 
SET default_currency = 'ZAR' 
WHERE default_currency = 'USD' OR default_currency IS NULL;

-- Update supplier-related tables if they exist
DO $$
BEGIN
    -- Check if suppliers table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'suppliers' AND column_name = 'currency'
    ) THEN
        UPDATE public.suppliers 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;

    -- Check if supplier_products table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'supplier_products' AND column_name = 'currency'
    ) THEN
        UPDATE public.supplier_products 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;

    -- Check if equipment_catalog table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'equipment_catalog' AND column_name = 'currency'
    ) THEN
        UPDATE public.equipment_catalog 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;

    -- Check if financial_accounts table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'financial_accounts' AND column_name = 'currency'
    ) THEN
        UPDATE public.financial_accounts 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;

    -- Check if invoices table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'invoices' AND column_name = 'currency'
    ) THEN
        UPDATE public.invoices 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;

    -- Check if quotes table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'quotes' AND column_name = 'currency'
    ) THEN
        UPDATE public.quotes 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;

    -- Check if purchase_orders table exists and has currency column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'purchase_orders' AND column_name = 'currency'
    ) THEN
        UPDATE public.purchase_orders 
        SET currency = 'ZAR' 
        WHERE currency = 'USD' OR currency IS NULL;
    END IF;
END $$;

-- Add comment to track this migration
COMMENT ON SCHEMA public IS 'Updated to use ZAR (South African Rand) as base currency - Migration 0004';

-- Create or update a platform_settings table for global configuration
CREATE TABLE IF NOT EXISTS public.platform_settings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key varchar(255) UNIQUE NOT NULL,
    setting_value text NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Insert or update platform currency setting
INSERT INTO public.platform_settings (setting_key, setting_value, description)
VALUES ('base_currency', 'ZAR', 'Platform base currency - South African Rand')
ON CONFLICT (setting_key) 
DO UPDATE SET 
    setting_value = 'ZAR',
    description = 'Platform base currency - South African Rand',
    updated_at = now();

-- Insert or update default region setting
INSERT INTO public.platform_settings (setting_key, setting_value, description)
VALUES ('default_region', 'South Africa', 'Default region for the platform')
ON CONFLICT (setting_key) 
DO UPDATE SET 
    setting_value = 'South Africa',
    description = 'Default region for the platform',
    updated_at = now();

-- Insert or update default timezone setting
INSERT INTO public.platform_settings (setting_key, setting_value, description)
VALUES ('default_timezone', 'Africa/Johannesburg', 'Default timezone for the platform')
ON CONFLICT (setting_key) 
DO UPDATE SET 
    setting_value = 'Africa/Johannesburg',
    description = 'Default timezone for the platform',
    updated_at = now();

-- Enable RLS on platform_settings
ALTER TABLE public.platform_settings ENABLE ROW LEVEL SECURITY;

-- Create policy for platform_settings (read-only for authenticated users)
CREATE POLICY "Platform settings are readable by authenticated users" ON public.platform_settings
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create policy for platform_settings (admin only for modifications)
CREATE POLICY "Platform settings are modifiable by admins only" ON public.platform_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );
