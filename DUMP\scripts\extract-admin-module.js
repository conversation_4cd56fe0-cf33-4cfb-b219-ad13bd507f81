#!/usr/bin/env node

/**
 * 🛡️ ADMIN AND SYSTEMS CONFIGURATION MODULE EXTRACTOR
 * 
 * This script extracts the complete Admin and Systems Configuration module
 * from the main QuantaSori platform according to the MODULE_EXTRACTION_PLAN.md
 * 
 * LIGHTS, CAMERA, ACTION! 🎬
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination paths
const SOURCE_DIR = path.resolve(__dirname, '../');
const DEST_DIR = path.resolve(__dirname, '../../DOCUECHANGE/modules/admin-systems-config');

// Module configuration
const MODULE_CONFIG = {
  name: 'Admin and Systems Configuration',
  version: '1.0.0',
  description: 'Complete admin dashboard with RBAC, user management, and system configuration',
  dependencies: [
    '@supabase/supabase-js',
    '@tanstack/react-query',
    'react',
    'react-dom',
    'react-router-dom',
    'lucide-react',
    'clsx',
    'tailwind-merge',
    'sonner'
  ]
};

// Files and directories to extract
const EXTRACTION_MAP = {
  // Admin Components
  'src/components/admin': 'src/components/admin',
  'src/pages/AdminDashboard.tsx': 'src/pages/AdminDashboard.tsx',
  'src/pages/UserManagement.tsx': 'src/pages/UserManagement.tsx',
  'src/pages/admin': 'src/pages/admin',
  
  // Authentication & Authorization
  'src/auth': 'src/auth',
  'src/components/auth': 'src/components/auth',
  'src/components/PermissionGuard.tsx': 'src/components/PermissionGuard.tsx',
  'src/components/ProtectedRoute.tsx': 'src/components/ProtectedRoute.tsx',
  
  // RBAC & Permissions
  'src/utils/rbac': 'src/utils/rbac',
  'src/types/rbac.ts': 'src/types/rbac.ts',
  'src/services/permissionsService.ts': 'src/services/permissionsService.ts',
  'src/hooks/usePermissions.ts': 'src/hooks/usePermissions.ts',
  'src/hooks/useUserPermissions.ts': 'src/hooks/useUserPermissions.ts',
  
  // User Management
  'src/context/UserManagementContext.tsx': 'src/context/UserManagementContext.tsx',
  'src/hooks/useModuleAccess.ts': 'src/hooks/useModuleAccess.ts',
  'src/hooks/useUserPreferences.ts': 'src/hooks/useUserPreferences.ts',
  'src/services/userPreferencesService.ts': 'src/services/userPreferencesService.ts',
  
  // Routes
  'src/routes/adminRoutes.tsx': 'src/routes/adminRoutes.tsx',
  'src/routes/coreSystemConfigRoutes.tsx': 'src/routes/coreSystemConfigRoutes.tsx',
  
  // Configuration
  'src/config': 'src/config',
  
  // Types
  'src/types/module-access.d.ts': 'src/types/module-access.d.ts',
  
  // Shared Components
  'src/components/ui': 'src/components/ui',
  'src/components/common': 'src/components/common',
  'src/components/shared': 'src/components/shared',
  
  // Layouts
  'src/layouts/MainLayout.tsx': 'src/layouts/MainLayout.tsx',
  'src/layouts/ProtectedLayout.tsx': 'src/layouts/ProtectedLayout.tsx',
  
  // Utilities
  'src/lib/utils.ts': 'src/lib/utils.ts',
  'src/integrations/supabase': 'src/integrations/supabase',
  
  // Styles
  'src/styles': 'src/styles',
  
  // Configuration files
  'tailwind.config.js': 'tailwind.config.js',
  'postcss.config.js': 'postcss.config.js',
  'tsconfig.json': 'tsconfig.json',
  'vite.config.ts': 'vite.config.ts',
  'components.json': 'components.json'
};

async function createModuleStructure() {
  console.log('🎬 LIGHTS, CAMERA, ACTION! Starting Admin Module Extraction...');
  
  try {
    // Ensure destination directory exists
    await fs.ensureDir(DEST_DIR);
    
    // Create module package.json
    const packageJson = {
      name: '@quantasori/admin-systems-config',
      version: MODULE_CONFIG.version,
      description: MODULE_CONFIG.description,
      main: 'src/index.ts',
      type: 'module',
      scripts: {
        dev: 'vite',
        build: 'tsc && vite build',
        preview: 'vite preview',
        lint: 'eslint . --ext ts,tsx',
        test: 'jest'
      },
      dependencies: MODULE_CONFIG.dependencies.reduce((deps, dep) => {
        deps[dep] = 'latest';
        return deps;
      }, {}),
      peerDependencies: {
        react: '>=18.0.0',
        'react-dom': '>=18.0.0'
      },
      keywords: ['admin', 'rbac', 'user-management', 'systems-config', 'quantasori'],
      author: 'QuantaSori Development Team',
      license: 'MIT'
    };
    
    await fs.writeJson(path.join(DEST_DIR, 'package.json'), packageJson, { spaces: 2 });
    console.log('✅ Created module package.json');
    
    // Create README
    const readme = `# 🛡️ Admin and Systems Configuration Module

${MODULE_CONFIG.description}

## Features

- **Complete Admin Dashboard** with comprehensive system overview
- **Role-Based Access Control (RBAC)** with granular permissions
- **User Management** with full CRUD operations
- **System Configuration** with real-time settings
- **Permission Management** with role assignment
- **Audit Logging** for security compliance
- **Module Toggle Panel** for feature management
- **Beta Feature Management** for controlled rollouts

## Installation

\`\`\`bash
npm install @quantasori/admin-systems-config
\`\`\`

## Usage

\`\`\`tsx
import { AdminDashboard, UserManagement } from '@quantasori/admin-systems-config';

function App() {
  return (
    <AdminDashboard>
      <UserManagement />
    </AdminDashboard>
  );
}
\`\`\`

## Components

- \`AdminDashboard\` - Main admin interface
- \`UserManagement\` - User CRUD operations
- \`PermissionGuard\` - Route protection
- \`ModuleTogglePanel\` - Feature toggles
- \`BetaFeatureManagement\` - Beta feature control

## License

MIT © QuantaSori Development Team
`;
    
    await fs.writeFile(path.join(DEST_DIR, 'README.md'), readme);
    console.log('✅ Created module README.md');
    
    return true;
  } catch (error) {
    console.error('❌ Error creating module structure:', error);
    return false;
  }
}

async function extractFiles() {
  console.log('🚀 Extracting admin module files...');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const [sourcePath, destPath] of Object.entries(EXTRACTION_MAP)) {
    try {
      const fullSourcePath = path.join(SOURCE_DIR, sourcePath);
      const fullDestPath = path.join(DEST_DIR, destPath);
      
      // Check if source exists
      if (await fs.pathExists(fullSourcePath)) {
        // Ensure destination directory exists
        await fs.ensureDir(path.dirname(fullDestPath));
        
        // Copy file or directory
        await fs.copy(fullSourcePath, fullDestPath, {
          overwrite: true,
          preserveTimestamps: true
        });
        
        console.log(`✅ Extracted: ${sourcePath} → ${destPath}`);
        successCount++;
      } else {
        console.log(`⚠️  Source not found: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Error extracting ${sourcePath}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n📊 Extraction Summary:`);
  console.log(`✅ Successfully extracted: ${successCount} items`);
  console.log(`❌ Errors: ${errorCount} items`);
  
  return errorCount === 0;
}

async function createModuleIndex() {
  console.log('📝 Creating module index file...');
  
  const indexContent = `/**
 * 🛡️ Admin and Systems Configuration Module
 * 
 * Complete admin dashboard with RBAC, user management, and system configuration
 * 
 * @version ${MODULE_CONFIG.version}
 * <AUTHOR> Development Team
 */

// Main Components
export { default as AdminDashboard } from './pages/AdminDashboard';
export { default as UserManagement } from './pages/UserManagement';

// Admin Components
export * from './components/admin/ModuleTogglePanel';
export * from './components/admin/BetaFeatureManagement';
export * from './components/admin/PermissionGuard';

// User Management Components
export * from './components/admin/users/UsersTab';
export * from './components/admin/users/RolesTab';
export * from './components/admin/users/PermissionsTab';
export * from './components/admin/users/AddUserDialog';
export * from './components/admin/users/EditUserDialog';
export * from './components/admin/users/AddRoleDialog';

// Database Management
export * from './components/admin/database/SqlQueryEditor';
export * from './components/admin/database/TablesList';
export * from './components/admin/database/QueryHistoryList';

// Documentation Management
export * from './components/admin/documentation/DocumentExplorer';
export * from './components/admin/documentation/DocumentViewer';
export * from './components/admin/documentation/DocumentUpload';

// Auth Components
export * from './components/auth';
export { default as PermissionGuard } from './components/PermissionGuard';
export { default as ProtectedRoute } from './components/ProtectedRoute';

// Hooks
export * from './hooks/usePermissions';
export * from './hooks/useUserPermissions';
export * from './hooks/useModuleAccess';
export * from './hooks/useUserPreferences';

// Services
export * from './services/permissionsService';
export * from './services/userPreferencesService';

// Types
export * from './types/rbac';
export * from './types/module-access';

// Context
export * from './context/UserManagementContext';

// Routes
export * from './routes/adminRoutes';
export * from './routes/coreSystemConfigRoutes';

// Utils
export * from './utils/rbac';
`;

  await fs.writeFile(path.join(DEST_DIR, 'src/index.ts'), indexContent);
  console.log('✅ Created module index file');
}

async function main() {
  console.log('🎬🎬🎬 ADMIN MODULE EXTRACTION - LIGHTS, CAMERA, ACTION! 🎬🎬🎬');
  console.log('='.repeat(70));
  
  try {
    // Step 1: Create module structure
    const structureCreated = await createModuleStructure();
    if (!structureCreated) {
      throw new Error('Failed to create module structure');
    }
    
    // Step 2: Extract files
    const filesExtracted = await extractFiles();
    if (!filesExtracted) {
      console.log('⚠️  Some files failed to extract, but continuing...');
    }
    
    // Step 3: Create module index
    await createModuleIndex();
    
    console.log('\n🎉🎉🎉 ADMIN MODULE EXTRACTION COMPLETE! 🎉🎉🎉');
    console.log(`📁 Module location: ${DEST_DIR}`);
    console.log('🚀 Ready for independent deployment and development!');
    
  } catch (error) {
    console.error('\n💥 EXTRACTION FAILED:', error.message);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
