/**
 * Platform Currency Settings Component
 * Centralized currency configuration for the entire platform
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  RefreshCw, 
  DollarSign, 
  Globe, 
  CheckCircle, 
  AlertCircle,
  Info
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { CURRENCY_CONFIG, getSupportedCurrencies, formatCurrency } from '@/config/currency';

interface CurrencySettings {
  base_currency: string;
  multi_currency_enabled: boolean;
  auto_conversion: boolean;
  default_locale: string;
  default_region: string;
  exchange_rate_provider: string;
  last_updated: string;
}

export function PlatformCurrencySettings() {
  const [settings, setSettings] = useState<CurrencySettings>({
    base_currency: CURRENCY_CONFIG.BASE_CURRENCY,
    multi_currency_enabled: false,
    auto_conversion: false,
    default_locale: CURRENCY_CONFIG.DEFAULT_LOCALE,
    default_region: CURRENCY_CONFIG.DEFAULT_REGION,
    exchange_rate_provider: 'manual',
    last_updated: new Date().toISOString(),
  });

  const [isLoading, setIsLoading] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  const supportedCurrencies = getSupportedCurrencies();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('platform_settings')
        .select('setting_key, setting_value')
        .in('setting_key', ['base_currency', 'default_region', 'default_timezone']);

      if (data && !error) {
        const settingsMap = data.reduce((acc, item) => {
          acc[item.setting_key] = item.setting_value;
          return acc;
        }, {} as Record<string, string>);

        setSettings(prev => ({
          ...prev,
          base_currency: settingsMap.base_currency || CURRENCY_CONFIG.BASE_CURRENCY,
          default_region: settingsMap.default_region || CURRENCY_CONFIG.DEFAULT_REGION,
        }));
      }
    } catch (error) {
      console.error('Failed to load currency settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingChange = (key: keyof CurrencySettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
      last_updated: new Date().toISOString(),
    }));
    setHasChanges(true);
    setSaveMessage(null);
  };

  const handleSave = async () => {
    setIsLoading(true);
    setSaveMessage(null);

    try {
      // Update platform_settings table
      const updates = [
        {
          setting_key: 'base_currency',
          setting_value: settings.base_currency,
          description: 'Platform base currency',
        },
        {
          setting_key: 'default_region',
          setting_value: settings.default_region,
          description: 'Default region for the platform',
        },
        {
          setting_key: 'multi_currency_enabled',
          setting_value: settings.multi_currency_enabled.toString(),
          description: 'Enable multi-currency support',
        },
      ];

      for (const update of updates) {
        const { error } = await supabase
          .from('platform_settings')
          .upsert(update, { onConflict: 'setting_key' });

        if (error) throw error;
      }

      // Update marketing_settings default currency if it exists
      const { error: marketingError } = await supabase
        .from('marketing_settings')
        .update({ default_currency: settings.base_currency })
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Update all records

      if (marketingError) {
        console.warn('Could not update marketing_settings:', marketingError);
      }

      setSaveMessage('Currency settings saved successfully! Changes will take effect immediately.');
      setHasChanges(false);
      
      // Auto-clear success message after 5 seconds
      setTimeout(() => setSaveMessage(null), 5000);

    } catch (error) {
      console.error('Failed to save currency settings:', error);
      setSaveMessage('Failed to save currency settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setSettings({
      base_currency: CURRENCY_CONFIG.BASE_CURRENCY,
      multi_currency_enabled: false,
      auto_conversion: false,
      default_locale: CURRENCY_CONFIG.DEFAULT_LOCALE,
      default_region: CURRENCY_CONFIG.DEFAULT_REGION,
      exchange_rate_provider: 'manual',
      last_updated: new Date().toISOString(),
    });
    setHasChanges(true);
    setSaveMessage(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Platform Currency Settings</h1>
          <p className="text-muted-foreground">
            Configure the base currency and regional settings for your entire platform
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <DollarSign className="w-3 h-3" />
          Current: {settings.base_currency}
        </Badge>
      </div>

      {/* Base Currency Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            Base Currency Configuration
          </CardTitle>
          <CardDescription>
            Set the primary currency for your platform. This affects all financial calculations, reporting, and integrations.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="base_currency">Base Currency</Label>
              <select
                id="base_currency"
                value={settings.base_currency}
                onChange={(e) => handleSettingChange('base_currency', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {supportedCurrencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency.symbol} {currency.code} - {currency.name}
                  </option>
                ))}
              </select>
              <p className="text-sm text-muted-foreground">
                Primary currency for all financial operations
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="default_region">Default Region</Label>
              <select
                id="default_region"
                value={settings.default_region}
                onChange={(e) => handleSettingChange('default_region', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="South Africa">South Africa</option>
                <option value="United States">United States</option>
                <option value="United Kingdom">United Kingdom</option>
                <option value="European Union">European Union</option>
                <option value="Canada">Canada</option>
                <option value="Australia">Australia</option>
                <option value="Global">Global</option>
              </select>
              <p className="text-sm text-muted-foreground">
                Default region for tax calculations and compliance
              </p>
            </div>
          </div>

          {/* Currency Preview */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Currency Format Preview:</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Small Amount:</span>
                <div className="font-mono">{formatCurrency(99.99, settings.base_currency)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Medium Amount:</span>
                <div className="font-mono">{formatCurrency(1299.99, settings.base_currency)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Large Amount:</span>
                <div className="font-mono">{formatCurrency(15999.99, settings.base_currency)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Very Large:</span>
                <div className="font-mono">{formatCurrency(125999.99, settings.base_currency)}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Currency Options</CardTitle>
          <CardDescription>
            Additional currency features and automation settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Multi-Currency Support</Label>
              <p className="text-sm text-muted-foreground">
                Allow transactions and reporting in multiple currencies
              </p>
            </div>
            <Switch
              checked={settings.multi_currency_enabled}
              onCheckedChange={(checked) => handleSettingChange('multi_currency_enabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Automatic Currency Conversion</Label>
              <p className="text-sm text-muted-foreground">
                Automatically convert foreign currencies to base currency
              </p>
            </div>
            <Switch
              checked={settings.auto_conversion}
              onCheckedChange={(checked) => handleSettingChange('auto_conversion', checked)}
              disabled={!settings.multi_currency_enabled}
            />
          </div>
        </CardContent>
      </Card>

      {/* Impact Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> Changing the base currency will affect all financial calculations, 
          reports, and integrations. Existing data will be preserved, but new transactions will use 
          the updated currency settings. Consider the impact on your WooCommerce and other integrations.
        </AlertDescription>
      </Alert>

      {/* Save Message */}
      {saveMessage && (
        <Alert className={saveMessage.includes('success') ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}>
          {saveMessage.includes('success') ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={saveMessage.includes('success') ? 'text-green-800' : 'text-red-800'}>
            {saveMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handleReset}
          disabled={isLoading}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Reset to Defaults
        </Button>

        <Button
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
          className="flex items-center gap-2"
        >
          <Save className="w-4 h-4" />
          {isLoading ? 'Saving...' : 'Save Currency Settings'}
        </Button>
      </div>
    </div>
  );
}

export default PlatformCurrencySettings;
