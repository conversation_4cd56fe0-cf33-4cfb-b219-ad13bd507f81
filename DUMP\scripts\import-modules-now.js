#!/usr/bin/env node

/**
 * 🚀 IMMEDIATE MODULE IMPORT SCRIPT
 *
 * LIGHTS, CAMERA, ACTION! 🎬
 *
 * This script imports all three modules FROM DOCUECHANGE TO the current project
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper functions
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dirPath}`);
  }
}

function pathExists(filePath) {
  return fs.existsSync(filePath);
}

function copyFileWithBackup(src, dest) {
  try {
    // Create backup if file exists
    if (fs.existsSync(dest)) {
      const backupPath = `${dest}.backup-${Date.now()}`;
      fs.copyFileSync(dest, backupPath);
      console.log(`💾 Backed up existing file: ${path.basename(dest)}`);
    }
    
    ensureDir(path.dirname(dest));
    fs.copyFileSync(src, dest);
    console.log(`✅ Imported: ${path.basename(src)}`);
    return true;
  } catch (error) {
    console.log(`❌ Error importing ${src}: ${error.message}`);
    return false;
  }
}

function copyDirRecursive(src, dest) {
  if (!fs.existsSync(src)) {
    console.log(`⚠️  Source not found: ${src}`);
    return 0;
  }

  ensureDir(dest);
  let count = 0;
  
  const items = fs.readdirSync(src);
  
  items.forEach(item => {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    
    if (fs.statSync(srcPath).isDirectory()) {
      count += copyDirRecursive(srcPath, destPath);
    } else {
      if (copyFileWithBackup(srcPath, destPath)) {
        count++;
      }
    }
  });
  
  return count;
}

// Paths
const CURRENT_DIR = __dirname; // Current NXT-WEB-DEV-X directory
const DOCUECHANGE_MODULES = path.resolve(__dirname, '../DOCUECHANGE/modules');

console.log('🎬🎬🎬 QUANTASORI MODULE IMPORT - LIGHTS, CAMERA, ACTION! 🎬🎬🎬');
console.log('='.repeat(80));
console.log(`📁 Source: ${DOCUECHANGE_MODULES}`);
console.log(`📁 Destination: ${CURRENT_DIR}`);
console.log('='.repeat(80));

// Import functions for each module
function importAdminModule() {
  console.log('\n🔐 IMPORTING ADMIN SYSTEMS CONFIGURATION MODULE...');
  console.log('-'.repeat(50));
  
  const adminModulePath = path.join(DOCUECHANGE_MODULES, 'admin-systems-config/src');
  let totalFiles = 0;
  
  if (!pathExists(adminModulePath)) {
    console.log('⚠️  Admin module not found in DOCUECHANGE');
    return 0;
  }
  
  // Import admin components
  const adminComponentsSrc = path.join(adminModulePath, 'components/admin');
  const adminComponentsDest = path.join(CURRENT_DIR, 'src/components/admin');
  if (pathExists(adminComponentsSrc)) {
    totalFiles += copyDirRecursive(adminComponentsSrc, adminComponentsDest);
  }
  
  // Import auth components
  const authSrc = path.join(adminModulePath, 'auth');
  const authDest = path.join(CURRENT_DIR, 'src/auth');
  if (pathExists(authSrc)) {
    totalFiles += copyDirRecursive(authSrc, authDest);
  }
  
  // Import auth components directory
  const authComponentsSrc = path.join(adminModulePath, 'components/auth');
  const authComponentsDest = path.join(CURRENT_DIR, 'src/components/auth');
  if (pathExists(authComponentsSrc)) {
    totalFiles += copyDirRecursive(authComponentsSrc, authComponentsDest);
  }
  
  // Import permission guards
  const permissionGuardSrc = path.join(adminModulePath, 'components/PermissionGuard.tsx');
  const permissionGuardDest = path.join(CURRENT_DIR, 'src/components/PermissionGuard.tsx');
  if (pathExists(permissionGuardSrc)) {
    if (copyFileWithBackup(permissionGuardSrc, permissionGuardDest)) totalFiles++;
  }
  
  const protectedRouteSrc = path.join(adminModulePath, 'components/ProtectedRoute.tsx');
  const protectedRouteDest = path.join(CURRENT_DIR, 'src/components/ProtectedRoute.tsx');
  if (pathExists(protectedRouteSrc)) {
    if (copyFileWithBackup(protectedRouteSrc, protectedRouteDest)) totalFiles++;
  }
  
  // Import RBAC utilities
  const rbacSrc = path.join(adminModulePath, 'utils/rbac');
  const rbacDest = path.join(CURRENT_DIR, 'src/utils/rbac');
  if (pathExists(rbacSrc)) {
    totalFiles += copyDirRecursive(rbacSrc, rbacDest);
  }
  
  // Import types
  const rbacTypesSrc = path.join(adminModulePath, 'types/rbac.ts');
  const rbacTypesDest = path.join(CURRENT_DIR, 'src/types/rbac.ts');
  if (pathExists(rbacTypesSrc)) {
    if (copyFileWithBackup(rbacTypesSrc, rbacTypesDest)) totalFiles++;
  }
  
  // Import hooks
  const hooksSrc = path.join(adminModulePath, 'hooks');
  const hooksDest = path.join(CURRENT_DIR, 'src/hooks');
  if (pathExists(hooksSrc)) {
    totalFiles += copyDirRecursive(hooksSrc, hooksDest);
  }
  
  // Import context
  const contextSrc = path.join(adminModulePath, 'context');
  const contextDest = path.join(CURRENT_DIR, 'src/context');
  if (pathExists(contextSrc)) {
    totalFiles += copyDirRecursive(contextSrc, contextDest);
  }
  
  // Import admin pages
  const adminPagesSrc = path.join(adminModulePath, 'pages');
  const adminPagesDest = path.join(CURRENT_DIR, 'src/pages/admin');
  if (pathExists(adminPagesSrc)) {
    totalFiles += copyDirRecursive(adminPagesSrc, adminPagesDest);
  }
  
  // Import admin routes
  const adminRoutesSrc = path.join(adminModulePath, 'routes/adminRoutes.tsx');
  const adminRoutesDest = path.join(CURRENT_DIR, 'src/routes/adminRoutes.tsx');
  if (pathExists(adminRoutesSrc)) {
    if (copyFileWithBackup(adminRoutesSrc, adminRoutesDest)) totalFiles++;
  }
  
  console.log(`📊 Admin module: ✅ ${totalFiles} files imported`);
  return totalFiles;
}

function importWooCommerceModule() {
  console.log('\n🛒 IMPORTING WOOCOMMERCE INTEGRATION MODULE...');
  console.log('-'.repeat(50));
  
  const wooModulePath = path.join(DOCUECHANGE_MODULES, 'woocommerce-integration/src');
  let totalFiles = 0;
  
  if (!pathExists(wooModulePath)) {
    console.log('⚠️  WooCommerce module not found in DOCUECHANGE');
    return 0;
  }
  
  // Import integration components
  const integrationsSrc = path.join(wooModulePath, 'components/integrations');
  const integrationsDest = path.join(CURRENT_DIR, 'src/components/integrations');
  if (pathExists(integrationsSrc)) {
    totalFiles += copyDirRecursive(integrationsSrc, integrationsDest);
  }
  
  // Import customer components
  const customersSrc = path.join(wooModulePath, 'components/customers');
  const customersDest = path.join(CURRENT_DIR, 'src/components/customers');
  if (pathExists(customersSrc)) {
    totalFiles += copyDirRecursive(customersSrc, customersDest);
  }
  
  // Import customer analytics
  const customerAnalyticsSrc = path.join(wooModulePath, 'components/customer-analytics');
  const customerAnalyticsDest = path.join(CURRENT_DIR, 'src/components/customer-analytics');
  if (pathExists(customerAnalyticsSrc)) {
    totalFiles += copyDirRecursive(customerAnalyticsSrc, customerAnalyticsDest);
  }
  
  // Import pages
  const pagesSrc = path.join(wooModulePath, 'pages');
  const pagesDest = path.join(CURRENT_DIR, 'src/pages');
  if (pathExists(pagesSrc)) {
    totalFiles += copyDirRecursive(pagesSrc, pagesDest);
  }
  
  // Import hooks
  const hooksSrc = path.join(wooModulePath, 'hooks');
  const hooksDest = path.join(CURRENT_DIR, 'src/hooks');
  if (pathExists(hooksSrc)) {
    totalFiles += copyDirRecursive(hooksSrc, hooksDest);
  }
  
  // Import routes
  const routesSrc = path.join(wooModulePath, 'routes');
  const routesDest = path.join(CURRENT_DIR, 'src/routes');
  if (pathExists(routesSrc)) {
    totalFiles += copyDirRecursive(routesSrc, routesDest);
  }
  
  // Import types
  const typesSrc = path.join(wooModulePath, 'types');
  const typesDest = path.join(CURRENT_DIR, 'src/types');
  if (pathExists(typesSrc)) {
    totalFiles += copyDirRecursive(typesSrc, typesDest);
  }
  
  console.log(`📊 WooCommerce module: ✅ ${totalFiles} files imported`);
  return totalFiles;
}

function importOdooModule() {
  console.log('\n🏢 IMPORTING ODOO INTEGRATION MODULE...');
  console.log('-'.repeat(50));
  
  const odooModulePath = path.join(DOCUECHANGE_MODULES, 'odoo-integration/src');
  let totalFiles = 0;
  
  if (!pathExists(odooModulePath)) {
    console.log('⚠️  Odoo module not found in DOCUECHANGE');
    return 0;
  }
  
  // Import integration components
  const integrationsSrc = path.join(odooModulePath, 'components/integrations');
  const integrationsDest = path.join(CURRENT_DIR, 'src/components/integrations');
  if (pathExists(integrationsSrc)) {
    totalFiles += copyDirRecursive(integrationsSrc, integrationsDest);
  }
  
  // Import financial components
  const financialSrc = path.join(odooModulePath, 'components/financial');
  const financialDest = path.join(CURRENT_DIR, 'src/components/financial');
  if (pathExists(financialSrc)) {
    totalFiles += copyDirRecursive(financialSrc, financialDest);
  }
  
  // Import project management components
  const pmSrc = path.join(odooModulePath, 'components/project-management');
  const pmDest = path.join(CURRENT_DIR, 'src/components/project-management');
  if (pathExists(pmSrc)) {
    totalFiles += copyDirRecursive(pmSrc, pmDest);
  }
  
  // Import suppliers components
  const suppliersSrc = path.join(odooModulePath, 'components/suppliers');
  const suppliersDest = path.join(CURRENT_DIR, 'src/components/suppliers');
  if (pathExists(suppliersSrc)) {
    totalFiles += copyDirRecursive(suppliersSrc, suppliersDest);
  }
  
  // Import layouts
  const layoutsSrc = path.join(odooModulePath, 'layouts');
  const layoutsDest = path.join(CURRENT_DIR, 'src/layouts');
  if (pathExists(layoutsSrc)) {
    totalFiles += copyDirRecursive(layoutsSrc, layoutsDest);
  }
  
  // Import pages
  const pagesSrc = path.join(odooModulePath, 'pages');
  const pagesDest = path.join(CURRENT_DIR, 'src/pages');
  if (pathExists(pagesSrc)) {
    totalFiles += copyDirRecursive(pagesSrc, pagesDest);
  }
  
  // Import routes
  const routesSrc = path.join(odooModulePath, 'routes');
  const routesDest = path.join(CURRENT_DIR, 'src/routes');
  if (pathExists(routesSrc)) {
    totalFiles += copyDirRecursive(routesSrc, routesDest);
  }
  
  // Import services
  const servicesSrc = path.join(odooModulePath, 'services');
  const servicesDest = path.join(CURRENT_DIR, 'src/services');
  if (pathExists(servicesSrc)) {
    totalFiles += copyDirRecursive(servicesSrc, servicesDest);
  }
  
  // Import types
  const typesSrc = path.join(odooModulePath, 'types');
  const typesDest = path.join(CURRENT_DIR, 'src/types');
  if (pathExists(typesSrc)) {
    totalFiles += copyDirRecursive(typesSrc, typesDest);
  }
  
  console.log(`📊 Odoo module: ✅ ${totalFiles} files imported`);
  return totalFiles;
}

// Main execution
async function main() {
  try {
    // Check if DOCUECHANGE modules directory exists
    if (!pathExists(DOCUECHANGE_MODULES)) {
      console.error(`❌ DOCUECHANGE modules directory not found: ${DOCUECHANGE_MODULES}`);
      process.exit(1);
    }
    
    // Import all modules
    const adminCount = importAdminModule();
    const wooCount = importWooCommerceModule();
    const odooCount = importOdooModule();
    
    const totalFiles = adminCount + wooCount + odooCount;
    
    console.log('\n🎉🎉🎉 MODULE IMPORT COMPLETE! 🎉🎉🎉');
    console.log('='.repeat(80));
    console.log(`📊 Total files imported: ${totalFiles}`);
    console.log(`🔐 Admin: ${adminCount} files`);
    console.log(`🛒 WooCommerce: ${wooCount} files`);
    console.log(`🏢 Odoo: ${odooCount} files`);
    console.log('='.repeat(80));
    console.log('\n📝 Next steps:');
    console.log('1. Review imported files for conflicts');
    console.log('2. Update imports and dependencies');
    console.log('3. Test module functionality');
    console.log('4. Update routing configuration');
    console.log('5. Run npm install if new dependencies were added');
    
  } catch (error) {
    console.error('💥 IMPORT FAILED:', error.message);
    process.exit(1);
  }
}

// Run the import
main();
