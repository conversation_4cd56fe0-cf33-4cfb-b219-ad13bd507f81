/**
 * WooCommerce Staging Quick Access Card
 * Provides quick access to staging system from integrations page
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  Settings, 
  Download, 
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface WooCommerceStagingCardProps {
  connectionStatus?: 'connected' | 'error' | 'unknown' | 'disabled' | 'pending' | 'not_configured';
  lastSyncAt?: string;
  stagingRecords?: {
    customers: number;
    products: number;
  };
}

export function WooCommerceStagingCard({ 
  connectionStatus = 'not_configured',
  lastSyncAt,
  stagingRecords 
}: WooCommerceStagingCardProps) {
  const navigate = useNavigate();

  const getStatusBadge = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'error':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>;
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'disabled':
        return <Badge variant="outline">Disabled</Badge>;
      default:
        return <Badge variant="secondary">Not Configured</Badge>;
    }
  };

  const isConfigured = connectionStatus !== 'not_configured';

  return (
    <Card className="border-2 border-dashed border-blue-200 bg-blue-50/50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg">WooCommerce Staging System</CardTitle>
          </div>
          {getStatusBadge()}
        </div>
        <CardDescription>
          Real-time data synchronization with staging area validation
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Status Information */}
        {isConfigured && (
          <div className="grid grid-cols-2 gap-4 p-3 bg-white rounded-lg border">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stagingRecords?.customers || 0}
              </div>
              <div className="text-xs text-muted-foreground">Customers Staged</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stagingRecords?.products || 0}
              </div>
              <div className="text-xs text-muted-foreground">Products Staged</div>
            </div>
          </div>
        )}

        {lastSyncAt && (
          <div className="text-sm text-muted-foreground">
            Last sync: {new Date(lastSyncAt).toLocaleString()}
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/staging')}
          >
            <Database className="h-4 w-4" />
            Staging Dashboard
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/configuration')}
          >
            <Settings className="h-4 w-4" />
            Configuration
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={!isConfigured}
            onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/staging')}
          >
            <Download className="h-4 w-4" />
            Batch Sync
          </Button>
        </div>

        {/* Setup Prompt */}
        {!isConfigured && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800 mb-2">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Setup Required</span>
            </div>
            <p className="text-sm text-yellow-700 mb-3">
              Configure your WooCommerce API connection to enable real-time staging and synchronization.
            </p>
            <Button
              size="sm"
              onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/configuration')}
              className="flex items-center gap-2"
            >
              Get Started
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Features List */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Key Features:</div>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Real-time data synchronization</li>
            <li>• Batch processing (100+ records)</li>
            <li>• Staging area validation</li>
            <li>• Error tracking & recovery</li>
            <li>• ZAR currency support</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
