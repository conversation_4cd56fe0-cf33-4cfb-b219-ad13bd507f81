#!/usr/bin/env node

/**
 * 🏢 ODOO INTEGRATION MODULE EXTRACTOR
 * 
 * This script extracts the complete Odoo ERP integration module
 * from the main QuantaSori platform according to the MODULE_EXTRACTION_PLAN.md
 * 
 * LIGHTS, CAMERA, ACTION! 🎬
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination paths
const SOURCE_DIR = path.resolve(__dirname, '../../NXT-WEB-DEV-X');
const DEST_DIR = path.resolve(__dirname, '../modules/odoo-integration');

// Module configuration
const MODULE_CONFIG = {
  name: 'Odoo Integration',
  version: '1.0.0',
  description: 'Complete Odoo ERP integration with business process automation',
  dependencies: [
    '@supabase/supabase-js',
    '@tanstack/react-query',
    'react',
    'react-dom',
    'react-router-dom',
    'lucide-react',
    'clsx',
    'tailwind-merge',
    'sonner',
    'axios',
    'xml2js'
  ]
};

// Files and directories to extract for Odoo
const EXTRACTION_MAP = {
  // Odoo Integration Components
  'src/components/integrations/odoo': 'src/components/odoo',
  
  // Business Governance (Odoo-related)
  'src/components/business-governance': 'src/components/business-governance',
  'src/pages/business-governance': 'src/pages/business-governance',
  'src/services/business-governance': 'src/services/business-governance',
  'src/types/business-governance.ts': 'src/types/business-governance.ts',
  'src/routes/businessGovernanceRoutes.tsx': 'src/routes/businessGovernanceRoutes.tsx',
  'src/utils/business-governance': 'src/utils/business-governance',
  'src/utils/business-governance-integrations.ts': 'src/utils/business-governance-integrations.ts',
  
  // Financial Integration (Odoo Financial modules)
  'src/components/financial': 'src/components/financial',
  'src/pages/financial': 'src/pages/financial',
  'src/services/financial': 'src/services/financial',
  'src/features/financial': 'src/features/financial',
  'src/types/financial.ts': 'src/types/financial.ts',
  'src/routes/financialRoutes.tsx': 'src/routes/financialRoutes.tsx',
  
  // HR Integration (Odoo HR modules)
  'src/components/hr': 'src/components/hr',
  'src/pages/hr': 'src/pages/hr',
  'src/services/hr': 'src/services/hr',
  'src/types/hr.ts': 'src/types/hr.ts',
  'src/routes/hrRoutes.tsx': 'src/routes/hrRoutes.tsx',
  
  // Project Management (Odoo Project modules)
  'src/components/project-management': 'src/components/project-management',
  'src/pages/project-management': 'src/pages/project-management',
  'src/services/project-management': 'src/services/project-management',
  'src/features/project-management': 'src/features/project-management',
  'src/types/project-management.ts': 'src/types/project-management.ts',
  'src/routes/projectManagementRoutes.tsx': 'src/routes/projectManagementRoutes.tsx',
  'src/layouts/ProjectManagementLayout.tsx': 'src/layouts/ProjectManagementLayout.tsx',
  
  // Inventory & Supply Chain (Odoo Inventory modules)
  'src/components/inventory': 'src/components/inventory',
  'src/components/supply-chain': 'src/components/supply-chain',
  'src/pages/supply-chain': 'src/pages/supply-chain',
  'src/features/supply-chain': 'src/features/supply-chain',
  'src/routes/supplyChainRoutes.tsx': 'src/routes/supplyChainRoutes.tsx',
  
  // Supplier Management (Odoo Vendor modules)
  'src/components/suppliers': 'src/components/suppliers',
  'src/components/supplier-costing': 'src/components/supplier-costing',
  'src/pages/suppliers': 'src/pages/suppliers',
  'src/pages/supplier-management': 'src/pages/supplier-management',
  'src/pages/supplier-costing': 'src/pages/supplier-costing',
  'src/features/suppliers': 'src/features/suppliers',
  'src/routes/supplierRoutes.tsx': 'src/routes/supplierRoutes.tsx',
  'src/routes/supplierManagementRoutes.tsx': 'src/routes/supplierManagementRoutes.tsx',
  'src/routes/supplierCostingRoutes.tsx': 'src/routes/supplierCostingRoutes.tsx',
  
  // Vendor Management
  'src/components/vendorDetail': 'src/components/vendorDetail',
  'src/pages/vendor-management': 'src/pages/vendor-management',
  'src/pages/vendors': 'src/pages/vendors',
  'src/routes/vendorRoutes.tsx': 'src/routes/vendorRoutes.tsx',
  'src/routes/vendorManagementRoutes.tsx': 'src/routes/vendorManagementRoutes.tsx',
  
  // Related Hooks
  'src/hooks/use-projects.ts': 'src/hooks/use-projects.ts',
  'src/hooks/use-project-members.ts': 'src/hooks/use-project-members.ts',
  'src/hooks/use-tasks.ts': 'src/hooks/use-tasks.ts',
  'src/hooks/use-suppliers.ts': 'src/hooks/use-suppliers.ts',
  'src/hooks/useSuppliers.ts': 'src/hooks/useSuppliers.ts',
  'src/hooks/use-supplier-costs.ts': 'src/hooks/use-supplier-costs.ts',
  'src/hooks/useSupplierDetail.ts': 'src/hooks/useSupplierDetail.ts',
  'src/hooks/useVendorDetail.ts': 'src/hooks/useVendorDetail.ts',
  
  // Related Types
  'src/types/supplier.ts': 'src/types/supplier.ts',
  'src/types/vendor.ts': 'src/types/vendor.ts',
  
  // Shared Components
  'src/components/ui': 'src/components/ui',
  'src/components/common': 'src/components/common',
  'src/components/shared': 'src/components/shared',
  
  // Utilities
  'src/lib/utils.ts': 'src/lib/utils.ts',
  'src/integrations/supabase': 'src/integrations/supabase',
  
  // Styles
  'src/styles': 'src/styles',
  
  // Configuration files
  'tailwind.config.js': 'tailwind.config.js',
  'postcss.config.js': 'postcss.config.js',
  'tsconfig.json': 'tsconfig.json',
  'vite.config.ts': 'vite.config.ts',
  'components.json': 'components.json'
};

async function createModuleStructure() {
  console.log('🎬 LIGHTS, CAMERA, ACTION! Starting Odoo Module Extraction...');
  
  try {
    // Ensure destination directory exists
    await fs.ensureDir(DEST_DIR);
    
    // Create module package.json
    const packageJson = {
      name: '@quantasori/odoo-integration',
      version: MODULE_CONFIG.version,
      description: MODULE_CONFIG.description,
      main: 'src/index.ts',
      type: 'module',
      scripts: {
        dev: 'vite',
        build: 'tsc && vite build',
        preview: 'vite preview',
        lint: 'eslint . --ext ts,tsx',
        test: 'jest',
        'sync:odoo': 'node scripts/sync-odoo.js',
        'migrate:data': 'node scripts/migrate-data.js'
      },
      dependencies: MODULE_CONFIG.dependencies.reduce((deps, dep) => {
        deps[dep] = 'latest';
        return deps;
      }, {}),
      peerDependencies: {
        react: '>=18.0.0',
        'react-dom': '>=18.0.0'
      },
      keywords: ['odoo', 'erp', 'integration', 'business-process', 'quantasori'],
      author: 'QuantaSori Development Team',
      license: 'MIT'
    };
    
    await fs.writeJson(path.join(DEST_DIR, 'package.json'), packageJson, { spaces: 2 });
    console.log('✅ Created Odoo module package.json');
    
    // Create README
    const readme = `# 🏢 Odoo Integration Module

${MODULE_CONFIG.description}

## Features

- **Complete ERP Integration** with Odoo systems
- **Financial Management** with accounting automation
- **HR Management** with employee lifecycle
- **Project Management** with task tracking
- **Inventory Management** with real-time stock
- **Supplier Management** with vendor relations
- **Business Governance** with compliance tracking
- **Supply Chain Management** with logistics
- **Vendor Management** with procurement
- **Data Synchronization** with bi-directional sync

## Installation

\`\`\`bash
npm install @quantasori/odoo-integration
\`\`\`

## Configuration

\`\`\`env
ODOO_URL=https://your-odoo-instance.com
ODOO_DATABASE=your_database
ODOO_USERNAME=your_username
ODOO_PASSWORD=your_password
ODOO_API_KEY=your_api_key
\`\`\`

## Usage

\`\`\`tsx
import { 
  OdooIntegrationWrapper, 
  ProjectManagement, 
  SupplierManagement,
  FinancialManagement 
} from '@quantasori/odoo-integration';

function App() {
  return (
    <OdooIntegrationWrapper>
      <ProjectManagement />
      <SupplierManagement />
      <FinancialManagement />
    </OdooIntegrationWrapper>
  );
}
\`\`\`

## Components

- \`OdooIntegrationWrapper\` - Main integration wrapper
- \`ProjectManagement\` - Project and task management
- \`SupplierManagement\` - Vendor and supplier operations
- \`FinancialManagement\` - Accounting and finance
- \`HRManagement\` - Human resources management
- \`InventoryManagement\` - Stock and inventory control

## API Integration

- \`/api/odoo/sync\` - Data synchronization
- \`/api/odoo/projects\` - Project operations
- \`/api/odoo/suppliers\` - Supplier operations
- \`/api/odoo/financial\` - Financial operations
- \`/api/odoo/hr\` - HR operations

## License

MIT © QuantaSori Development Team
`;
    
    await fs.writeFile(path.join(DEST_DIR, 'README.md'), readme);
    console.log('✅ Created Odoo module README.md');
    
    return true;
  } catch (error) {
    console.error('❌ Error creating Odoo module structure:', error);
    return false;
  }
}

async function extractFiles() {
  console.log('🚀 Extracting Odoo module files...');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const [sourcePath, destPath] of Object.entries(EXTRACTION_MAP)) {
    try {
      const fullSourcePath = path.join(SOURCE_DIR, sourcePath);
      const fullDestPath = path.join(DEST_DIR, destPath);
      
      // Check if source exists
      if (await fs.pathExists(fullSourcePath)) {
        // Ensure destination directory exists
        await fs.ensureDir(path.dirname(fullDestPath));
        
        // Copy file or directory
        await fs.copy(fullSourcePath, fullDestPath, {
          overwrite: true,
          preserveTimestamps: true
        });
        
        console.log(`✅ Extracted: ${sourcePath} → ${destPath}`);
        successCount++;
      } else {
        console.log(`⚠️  Source not found: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Error extracting ${sourcePath}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n📊 Odoo Extraction Summary:`);
  console.log(`✅ Successfully extracted: ${successCount} items`);
  console.log(`❌ Errors: ${errorCount} items`);
  
  return errorCount === 0;
}

async function createModuleIndex() {
  console.log('📝 Creating Odoo module index file...');
  
  const indexContent = `/**
 * 🏢 Odoo Integration Module
 * 
 * Complete Odoo ERP integration with business process automation
 * 
 * @version ${MODULE_CONFIG.version}
 * <AUTHOR> Development Team
 */

// Main Integration Component
export { default as OdooIntegrationWrapper } from './components/odoo/OdooIntegrationWrapper';

// Business Governance
export * from './components/business-governance';
export * from './pages/business-governance';
export * from './services/business-governance';

// Financial Management
export * from './components/financial';
export * from './pages/financial';
export * from './services/financial';
export * from './features/financial';

// HR Management
export * from './components/hr';
export * from './pages/hr';
export * from './services/hr';

// Project Management
export * from './components/project-management';
export * from './pages/project-management';
export * from './services/project-management';
export * from './features/project-management';
export { default as ProjectManagementLayout } from './layouts/ProjectManagementLayout';

// Supply Chain & Inventory
export * from './components/inventory';
export * from './components/supply-chain';
export * from './pages/supply-chain';
export * from './features/supply-chain';

// Supplier & Vendor Management
export * from './components/suppliers';
export * from './components/supplier-costing';
export * from './components/vendorDetail';
export * from './pages/suppliers';
export * from './pages/supplier-management';
export * from './pages/supplier-costing';
export * from './pages/vendor-management';
export * from './pages/vendors';
export * from './features/suppliers';

// Hooks
export * from './hooks/use-projects';
export * from './hooks/use-project-members';
export * from './hooks/use-tasks';
export * from './hooks/use-suppliers';
export * from './hooks/useSuppliers';
export * from './hooks/use-supplier-costs';
export * from './hooks/useSupplierDetail';
export * from './hooks/useVendorDetail';

// Types
export * from './types/business-governance';
export * from './types/financial';
export * from './types/hr';
export * from './types/project-management';
export * from './types/supplier';
export * from './types/vendor';

// Routes
export * from './routes/businessGovernanceRoutes';
export * from './routes/financialRoutes';
export * from './routes/hrRoutes';
export * from './routes/projectManagementRoutes';
export * from './routes/supplyChainRoutes';
export * from './routes/supplierRoutes';
export * from './routes/supplierManagementRoutes';
export * from './routes/supplierCostingRoutes';
export * from './routes/vendorRoutes';
export * from './routes/vendorManagementRoutes';

// Utilities
export * from './utils/business-governance';
export * from './utils/business-governance-integrations';
`;

  await fs.writeFile(path.join(DEST_DIR, 'src/index.ts'), indexContent);
  console.log('✅ Created Odoo module index file');
}

async function main() {
  console.log('🎬🎬🎬 ODOO MODULE EXTRACTION - LIGHTS, CAMERA, ACTION! 🎬🎬🎬');
  console.log('='.repeat(70));
  
  try {
    // Step 1: Create module structure
    const structureCreated = await createModuleStructure();
    if (!structureCreated) {
      throw new Error('Failed to create Odoo module structure');
    }
    
    // Step 2: Extract files
    const filesExtracted = await extractFiles();
    if (!filesExtracted) {
      console.log('⚠️  Some Odoo files failed to extract, but continuing...');
    }
    
    // Step 3: Create module index
    await createModuleIndex();
    
    console.log('\n🎉🎉🎉 ODOO MODULE EXTRACTION COMPLETE! 🎉🎉🎉');
    console.log(`📁 Module location: ${DEST_DIR}`);
    console.log('🚀 Ready for independent deployment and ERP integration!');
    
  } catch (error) {
    console.error('\n💥 ODOO EXTRACTION FAILED:', error.message);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
