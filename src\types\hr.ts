// HR Module Type Definitions
// Comprehensive types for the HR & People Management module

// ============================================================================
// BASE ORGANIZATIONAL TYPES
// ============================================================================

export interface Organization {
  id: string;
  name: string;
  legal_name: string;
  tax_id?: string;
  registration_number?: string;
  industry?: string;
  employee_count: number;
  headquarters_address?: string;
  phone?: string;
  email?: string;
  website?: string;
  settings: OrganizationSettings;
  compliance_config: ComplianceConfig;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface OrganizationSettings {
  timezone?: string;
  currency?: string;
  date_format?: string;
  work_week_days?: number[];
  standard_work_hours?: number;
  fiscal_year_start?: string;
  language?: string;
  country?: string;
  payroll_frequency?: PayrollFrequency;
  probation_period_days?: number;
  notice_period_days?: number;
  annual_leave_days?: number;
  sick_leave_days?: number;
  public_holidays?: string[];
}

export interface ComplianceConfig {
  gdpr_enabled?: boolean;
  data_retention_years?: number;
  audit_trail_enabled?: boolean;
  background_check_required?: boolean;
  drug_testing_required?: boolean;
  safety_training_required?: boolean;
  compliance_officer_email?: string;
  regulatory_requirements?: string[];
}

// ============================================================================
// DEPARTMENT TYPES
// ============================================================================

export interface Department {
  id: string;
  organization_id: string;
  parent_department_id?: string;
  name: string;
  code: string;
  description?: string;
  manager_id?: string;
  cost_center_code?: string;
  budget_allocation?: BudgetAllocation;
  location?: string;
  email?: string;
  phone?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Computed/joined fields
  manager?: Employee;
  parent_department?: Department;
  sub_departments?: Department[];
  employees?: Employee[];
  employee_count?: number;
}

export interface BudgetAllocation {
  annual_budget?: number;
  currency?: string;
  budget_year?: number;
  allocated_categories?: {
    salaries?: number;
    benefits?: number;
    training?: number;
    equipment?: number;
    operations?: number;
    other?: number;
  };
  spent_to_date?: number;
  remaining_budget?: number;
}

// ============================================================================
// POSITION TYPES
// ============================================================================

export interface Position {
  id: string;
  organization_id: string;
  department_id?: string;
  title: string;
  code: string;
  description?: string;
  requirements?: string;
  responsibilities?: string;
  salary_range: SalaryRange;
  employment_type: EmploymentType;
  level: number;
  reports_to_position_id?: string;
  location?: string;
  is_remote_eligible: boolean;
  required_skills?: string[];
  preferred_skills?: string[];
  education_requirements?: string;
  experience_years_min: number;
  experience_years_max?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Computed/joined fields
  department?: Department;
  reports_to_position?: Position;
  current_employees?: Employee[];
  open_positions_count?: number;
}

export interface SalaryRange {
  min_salary?: number;
  max_salary?: number;
  currency?: string;
  pay_frequency?: PayrollFrequency;
  includes_commission?: boolean;
  commission_structure?: string;
  benefits_value?: number;
  total_compensation_min?: number;
  total_compensation_max?: number;
}

// ============================================================================
// EMPLOYEE TYPES
// ============================================================================

export interface Employee {
  id: string;
  organization_id: string;
  department_id?: string;
  position_id?: string;
  manager_id?: string;
  user_id?: string;
  employee_number: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  preferred_name?: string;
  email: string;
  personal_email?: string;
  phone?: string;
  mobile_phone?: string;
  date_of_birth?: string;
  gender?: Gender;
  nationality?: string;
  marital_status?: MaritalStatus;
  address?: string;
  postal_code?: string;
  city?: string;
  state_province?: string;
  country?: string;
  emergency_contact_name?: string;
  emergency_contact_relationship?: string;
  emergency_contact_phone?: string;
  emergency_contact_email?: string;
  hire_date: string;
  probation_end_date?: string;
  termination_date?: string;
  termination_reason?: string;
  employment_status: EmploymentStatus;
  work_location?: string;
  is_remote: boolean;
  personal_details: PersonalDetails;
  system_access: SystemAccess;
  benefits_info: BenefitsInfo;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Computed/joined fields
  full_name?: string;
  display_name?: string;
  department?: Department;
  position?: Position;
  manager?: Employee;
  direct_reports?: Employee[];
  current_contract?: Contract;
  years_of_service?: number;
  days_until_probation_end?: number;
  age?: number;
}

export interface PersonalDetails {
  profile_picture_url?: string;
  bio?: string;
  skills?: string[];
  certifications?: Certification[];
  languages?: Language[];
  hobbies?: string[];
  linkedin_url?: string;
  github_url?: string;
  portfolio_url?: string;
  notes?: string;
  custom_fields?: Record<string, any>;
}

export interface SystemAccess {
  roles?: string[];
  permissions?: string[];
  last_login?: string;
  login_count?: number;
  system_groups?: string[];
  access_level?: string;
  two_factor_enabled?: boolean;
  password_last_changed?: string;
  account_locked?: boolean;
  login_attempts?: number;
}

export interface BenefitsInfo {
  health_insurance?: BenefitEnrollment;
  dental_insurance?: BenefitEnrollment;
  vision_insurance?: BenefitEnrollment;
  life_insurance?: BenefitEnrollment;
  retirement_plan?: BenefitEnrollment;
  vacation_days_accrued?: number;
  sick_days_accrued?: number;
  personal_days_accrued?: number;
  benefits_start_date?: string;
  dependents?: Dependent[];
  beneficiaries?: Beneficiary[];
}

export interface BenefitEnrollment {
  enrolled: boolean;
  plan_name?: string;
  coverage_type?: string;
  monthly_premium?: number;
  employer_contribution?: number;
  employee_contribution?: number;
  effective_date?: string;
  end_date?: string;
}

export interface Dependent {
  name: string;
  relationship: string;
  date_of_birth?: string;
  ssn_last_four?: string;
  covered_benefits?: string[];
}

export interface Beneficiary {
  name: string;
  relationship: string;
  percentage: number;
  contact_info?: string;
  benefit_types?: string[];
}

export interface Certification {
  name: string;
  issuing_organization: string;
  issue_date?: string;
  expiry_date?: string;
  credential_id?: string;
  verification_url?: string;
}

export interface Language {
  language: string;
  proficiency: 'Basic' | 'Conversational' | 'Fluent' | 'Native';
  can_read?: boolean;
  can_write?: boolean;
  can_speak?: boolean;
}

// ============================================================================
// ENUMS AND CONSTANTS
// ============================================================================

export enum EmploymentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  TERMINATED = 'TERMINATED',
  ON_LEAVE = 'ON_LEAVE',
  SUSPENDED = 'SUSPENDED',
  PROBATION = 'PROBATION',
  NOTICE_PERIOD = 'NOTICE_PERIOD'
}

export enum EmploymentType {
  PERMANENT = 'PERMANENT',
  FIXED_TERM = 'FIXED_TERM',
  CONTRACTOR = 'CONTRACTOR',
  INTERN = 'INTERN',
  PART_TIME = 'PART_TIME',
  TEMPORARY = 'TEMPORARY',
  CONSULTANT = 'CONSULTANT'
}

export enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
  NON_BINARY = 'Non-binary',
  PREFER_NOT_TO_SAY = 'Prefer not to say',
  OTHER = 'Other'
}

export enum MaritalStatus {
  SINGLE = 'Single',
  MARRIED = 'Married',
  DIVORCED = 'Divorced',
  WIDOWED = 'Widowed',
  SEPARATED = 'Separated',
  DOMESTIC_PARTNERSHIP = 'Domestic Partnership',
  PREFER_NOT_TO_SAY = 'Prefer not to say'
}

export enum PayrollFrequency {
  WEEKLY = 'WEEKLY',
  BI_WEEKLY = 'BI_WEEKLY',
  SEMI_MONTHLY = 'SEMI_MONTHLY',
  MONTHLY = 'MONTHLY'
}

// ============================================================================
// CONTRACT TYPES (for Phase 2)
// ============================================================================

export interface Contract {
  id: string;
  employee_id: string;
  organization_id: string;
  contract_number: string;
  contract_type: ContractType;
  start_date: string;
  end_date?: string;
  salary: number;
  currency: string;
  payment_frequency: PayrollFrequency;
  benefits: ContractBenefits;
  terms_conditions: TermsConditions;
  status: ContractStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Computed fields
  employee?: Employee;
  is_current?: boolean;
  days_until_expiry?: number;
}

export enum ContractType {
  PERMANENT = 'PERMANENT',
  FIXED_TERM = 'FIXED_TERM',
  CONTRACTOR = 'CONTRACTOR',
  INTERN = 'INTERN',
  PART_TIME = 'PART_TIME'
}

export enum ContractStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED',
  RENEWED = 'RENEWED'
}

export interface ContractBenefits {
  health_insurance?: boolean;
  dental_insurance?: boolean;
  vision_insurance?: boolean;
  life_insurance?: boolean;
  retirement_plan?: boolean;
  paid_time_off_days?: number;
  sick_leave_days?: number;
  personal_days?: number;
  professional_development_budget?: number;
  remote_work_allowed?: boolean;
  flexible_hours?: boolean;
  company_car?: boolean;
  parking_allowance?: number;
  other_benefits?: string[];
}

export interface TermsConditions {
  probation_period_days?: number;
  notice_period_days?: number;
  non_compete_clause?: boolean;
  non_disclosure_clause?: boolean;
  intellectual_property_clause?: boolean;
  termination_conditions?: string[];
  renewal_conditions?: string[];
  performance_review_frequency?: string;
  bonus_eligibility?: boolean;
  overtime_eligibility?: boolean;
  travel_requirements?: string;
  relocation_requirements?: string;
}

// ============================================================================
// FILTERING AND SEARCHING TYPES
// ============================================================================

export interface EmployeeFilters {
  organization_id?: string;
  department_id?: string;
  position_id?: string;
  manager_id?: string;
  employment_status?: EmploymentStatus[];
  employment_type?: EmploymentType[];
  location?: string;
  is_remote?: boolean;
  hire_date_from?: string;
  hire_date_to?: string;
  is_active?: boolean;
  search_query?: string; // For name, email, employee_number search
}

export interface DepartmentFilters {
  organization_id?: string;
  parent_department_id?: string;
  manager_id?: string;
  is_active?: boolean;
  search_query?: string;
}

export interface PositionFilters {
  organization_id?: string;
  department_id?: string;
  employment_type?: EmploymentType[];
  level_min?: number;
  level_max?: number;
  is_remote_eligible?: boolean;
  location?: string;
  is_active?: boolean;
  search_query?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface HRApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    has_more?: boolean;
  };
}

export interface PaginatedResponse<T> extends HRApiResponse<T[]> {
  pagination: {
    current_page: number;
    total_pages: number;
    page_size: number;
    total_count: number;
    has_previous: boolean;
    has_next: boolean;
  };
}

// ============================================================================
// SERVICE INTERFACE TYPES
// ============================================================================

export interface HRServiceInterface<T> {
  create(data: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<T>;
  getById(id: string, organizationId: string): Promise<T | null>;
  update(id: string, data: Partial<T>, organizationId: string): Promise<T | null>;
  delete(id: string, organizationId: string): Promise<void>;
  getByOrganization(organizationId: string, filters?: any): Promise<T[]>;
}

export interface EmployeeServiceInterface extends HRServiceInterface<Employee> {
  getByEmployeeNumber(employeeNumber: string, organizationId: string): Promise<Employee | null>;
  getByEmail(email: string, organizationId: string): Promise<Employee | null>;
  getByManager(managerId: string, organizationId: string): Promise<Employee[]>;
  getByDepartment(departmentId: string, organizationId: string): Promise<Employee[]>;
  search(query: string, organizationId: string, filters?: EmployeeFilters): Promise<Employee[]>;
  updateEmploymentStatus(id: string, status: EmploymentStatus, organizationId: string): Promise<Employee | null>;
  terminate(id: string, terminationDate: string, reason: string, organizationId: string): Promise<Employee | null>;
}

// ============================================================================
// VALIDATION TYPES
// ============================================================================

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface EmployeeValidation {
  isValidEmail(email: string): boolean;
  isValidEmployeeNumber(employeeNumber: string, organizationId: string): Promise<boolean>;
  isValidPhoneNumber(phone: string): boolean;
  isValidDateOfBirth(date: string): boolean;
  isValidHireDate(date: string): boolean;
  validateEmployeeData(data: Partial<Employee>): ValidationError[];
}

// ============================================================================
// AUDIT AND LOGGING TYPES
// ============================================================================

export interface HRAuditLog {
  id: string;
  organization_id: string;
  user_id?: string;
  employee_id?: string;
  action: HRAuditAction;
  entity_type: HREntityType;
  entity_id: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
}

export enum HRAuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  VIEW = 'VIEW',
  EXPORT = 'EXPORT',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT'
}

export enum HREntityType {
  EMPLOYEE = 'EMPLOYEE',
  DEPARTMENT = 'DEPARTMENT',
  POSITION = 'POSITION',
  CONTRACT = 'CONTRACT',
  ORGANIZATION = 'ORGANIZATION'
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type CreateEmployeeData = Omit<Employee, 'id' | 'created_at' | 'updated_at' | 'is_active'>;
export type UpdateEmployeeData = Partial<Omit<Employee, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>;

export type CreateDepartmentData = Omit<Department, 'id' | 'created_at' | 'updated_at' | 'is_active'>;
export type UpdateDepartmentData = Partial<Omit<Department, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>;

export type CreatePositionData = Omit<Position, 'id' | 'created_at' | 'updated_at' | 'is_active'>;
export type UpdatePositionData = Partial<Omit<Position, 'id' | 'organization_id' | 'created_at' | 'updated_at'>>;

export type CreateOrganizationData = Omit<Organization, 'id' | 'created_at' | 'updated_at' | 'employee_count' | 'is_active'>;
export type UpdateOrganizationData = Partial<Omit<Organization, 'id' | 'created_at' | 'updated_at' | 'employee_count'>>;

// ============================================================================
// CONSTANTS
// ============================================================================

export const HR_CONSTANTS = {
  MAX_EMPLOYEE_NUMBER_LENGTH: 50,
  MAX_NAME_LENGTH: 100,
  MAX_EMAIL_LENGTH: 255,
  MAX_PHONE_LENGTH: 50,
  MAX_DESCRIPTION_LENGTH: 2000,
  DEFAULT_PAGE_SIZE: 25,
  MAX_PAGE_SIZE: 100,
  MIN_EMPLOYEE_AGE: 16,
  MAX_EMPLOYEE_AGE: 120,
  DEFAULT_CURRENCY: 'ZAR',
  DEFAULT_COUNTRY: 'South Africa',
} as const;

export const EMPLOYMENT_STATUS_LABELS: Record<EmploymentStatus, string> = {
  [EmploymentStatus.ACTIVE]: 'Active',
  [EmploymentStatus.INACTIVE]: 'Inactive',
  [EmploymentStatus.TERMINATED]: 'Terminated',
  [EmploymentStatus.ON_LEAVE]: 'On Leave',
  [EmploymentStatus.SUSPENDED]: 'Suspended',
  [EmploymentStatus.PROBATION]: 'Probation',
  [EmploymentStatus.NOTICE_PERIOD]: 'Notice Period',
};

export const EMPLOYMENT_TYPE_LABELS: Record<EmploymentType, string> = {
  [EmploymentType.PERMANENT]: 'Permanent',
  [EmploymentType.FIXED_TERM]: 'Fixed Term',
  [EmploymentType.CONTRACTOR]: 'Contractor',
  [EmploymentType.INTERN]: 'Intern',
  [EmploymentType.PART_TIME]: 'Part Time',
  [EmploymentType.TEMPORARY]: 'Temporary',
  [EmploymentType.CONSULTANT]: 'Consultant',
};
