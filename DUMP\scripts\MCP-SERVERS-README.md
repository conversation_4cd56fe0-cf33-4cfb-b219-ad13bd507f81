# MCP Servers Setup for NXT-WEB-DEV-X

This repository includes a comprehensive setup for **11 Model Context Protocol (MCP) servers** running in individual Docker containers within VSCode development environment.

## 🚀 Quick Start

1. **Open in VSCode Dev Container**
   ```bash
   # VSCode will automatically detect the devcontainer configuration
   # Click "Reopen in Container" when prompted
   ```

2. **Configure API Keys**
   ```bash
   # Update the .env file with your actual API keys
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Build and Start MCP Servers**
   ```bash
   # Build all MCP server images
   bash .devcontainer/scripts/build-all-servers.sh
   
   # Start all containers
   docker-compose -f .devcontainer/docker-compose.yml up -d
   
   # Check health status
   node mcp-config/health-check.js
   ```

## 📋 Available MCP Servers

| Server | Port | Description | API Key Required |
|--------|------|-------------|------------------|
| **Brave Search** | 8080 | Web search using Brave Search API | `BRAVE_API_KEY` |
| **Tavily** | 8081 | AI-powered search and content extraction | `TAVILY_API_KEY` |
| **FireCrawl** | 8082 | Web scraping and crawling | `FIRECRAWL_API_KEY` |
| **Context7** | 8083 | Context storage with Upstash Redis | `UPSTASH_REDIS_*` |
| **Notion** | 8084 | Notion workspace integration | `NOTION_API_KEY` |
| **Desktop Commander** | 8085 | Desktop automation and commands | None |
| **Taskmaster** | 8086 | Task management and tracking | None |
| **Supabase** | 8087 | Supabase database operations | `SUPABASE_ACCESS_TOKEN` |
| **Browser Tools** | 8088 | Browser automation with Puppeteer | None |
| **Magic MCP** | 8089 | 21st Dev Magic tools | None |
| **Neo4j** | 8090 | Neo4j graph database operations | `NEO4J_*` |

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# MCP Server API Keys
BRAVE_API_KEY=your_brave_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
UPSTASH_REDIS_REST_URL=your_upstash_redis_url_here
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here
NOTION_API_KEY=your_notion_api_key_here
SUPABASE_ACCESS_TOKEN=your_supabase_access_token_here
SUPABASE_PROJECT_ID=your_supabase_project_id_here
NEO4J_URI=your_neo4j_uri_here
NEO4J_USERNAME=your_neo4j_username_here
NEO4J_PASSWORD=your_neo4j_password_here
```

### VSCode Settings

The MCP servers are automatically configured in `.vscode/settings.json` with the `mcp.servers` configuration that connects to each Docker container.

## 🛠️ Usage Examples

### Brave Search
```javascript
// Search the web
{
  "tool": "brave_search",
  "arguments": {
    "query": "latest AI developments",
    "count": 10,
    "country": "US"
  }
}
```

### Tavily Search
```javascript
// AI-powered search with answer
{
  "tool": "tavily_search",
  "arguments": {
    "query": "How to implement MCP servers?",
    "include_answer": true,
    "max_results": 5
  }
}
```

### FireCrawl
```javascript
// Scrape a website
{
  "tool": "firecrawl_scrape",
  "arguments": {
    "url": "https://example.com",
    "formats": ["markdown", "html"],
    "onlyMainContent": true
  }
}
```

### Context7 (Redis Storage)
```javascript
// Store context data
{
  "tool": "context7_store",
  "arguments": {
    "key": "project_notes",
    "content": "Important project information...",
    "metadata": {
      "title": "Project Notes",
      "tags": ["project", "important"]
    }
  }
}
```

### Notion
```javascript
// Search Notion pages
{
  "tool": "notion_search",
  "arguments": {
    "query": "meeting notes",
    "page_size": 10
  }
}
```

## 🏥 Health Monitoring

Check the health of all MCP servers:

```bash
# Run health check
node mcp-config/health-check.js

# Check individual server
curl http://localhost:8080/health  # Brave Search
curl http://localhost:8081/health  # Tavily
# ... etc for other ports
```

## 🐳 Docker Management

```bash
# View running containers
docker ps

# View logs for a specific server
docker logs mcp-brave-search

# Restart a specific server
docker restart mcp-tavily

# Stop all MCP servers
docker-compose -f .devcontainer/docker-compose.yml down

# Rebuild a specific server
docker build -t mcp-notion:latest .devcontainer/mcp-servers/notion/
```

## 🔍 Troubleshooting

### Common Issues

1. **API Key Not Configured**
   - Check your `.env` file
   - Ensure environment variables are properly set
   - Restart the container after updating keys

2. **Container Not Starting**
   ```bash
   # Check container logs
   docker logs mcp-[server-name]
   
   # Check if port is already in use
   netstat -tulpn | grep 808[0-9]
   ```

3. **Health Check Failing**
   ```bash
   # Check if container is running
   docker ps | grep mcp-
   
   # Test direct connection
   curl -v http://localhost:8080/health
   ```

### Logs and Debugging

```bash
# View all MCP container logs
docker-compose -f .devcontainer/docker-compose.yml logs

# Follow logs for a specific server
docker logs -f mcp-brave-search

# Check container resource usage
docker stats
```

## 🔄 Updates and Maintenance

### Updating MCP Servers

```bash
# Pull latest changes
git pull

# Rebuild all servers
bash .devcontainer/scripts/build-all-servers.sh

# Restart containers
docker-compose -f .devcontainer/docker-compose.yml restart
```

### Adding New MCP Servers

1. Create new directory in `.devcontainer/mcp-servers/[server-name]/`
2. Add Dockerfile, package.json, and server.js
3. Update docker-compose.yml
4. Update .vscode/settings.json
5. Rebuild and restart

## 📚 Resources

- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [MCP SDK Documentation](https://github.com/modelcontextprotocol/sdk)
- [Individual Server Documentation](./docs/mcp-servers/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your MCP server implementation
4. Update documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
