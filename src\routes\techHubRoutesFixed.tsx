import React, { lazy, Suspense } from 'react';
import { Route, Routes, Navigate } from 'react-router-dom';
import TechHubLayout from '../components/layout/TechHubLayout';
import TechHubErrorBoundary from '../components/error-boundaries/TechHubErrorBoundary';

// Import only existing components to prevent errors
const TechHubDashboard = lazy(() => import('../pages/techhub/TechHubDashboard'));
const TechHubSettings = lazy(() => import('../pages/techhub/TechHubSettings'));
const TechHubDocumentation = lazy(() => import('../pages/techhub/TechHubDocumentation'));
const TechHubAnalytics = lazy(() => import('../pages/techhub/TechHubAnalytics'));
const TechHubTeam = lazy(() => import('../pages/techhub/TechHubTeam'));
const APITester = lazy(() => import('../pages/techhub/APITester'));
const AddAPIEndpoint = lazy(() => import('../pages/techhub/AddAPIEndpoint'));
const APIProviders = lazy(() => import('../pages/techhub/APIProviders'));

// Import existing pages from main pages directory
const TechHubPersonas = lazy(() => import('../pages/TechHubPersonas'));
const TechHubTechnicalConfig = lazy(() => import('../pages/TechHubTechnicalConfig'));
const ApiManagementPage = lazy(() => import('../pages/ApiManagementPage'));
const RequestyPage = lazy(() => import('../pages/RequestyPage'));
const IntegrationsPage = lazy(() => import('../pages/IntegrationsPage'));

// WooCommerce Integration Components
const WooCommerceStagingDashboard = lazy(() => import('../components/woocommerce/WooCommerceStagingDashboard'));
const WooCommerceConfiguration = lazy(() => import('../components/woocommerce/WooCommerceConfiguration'));

// Integration pages are now handled within IntegrationsPage

// Import existing cloud services
const CloudServicesIndex = lazy(() => import('../pages/cloud-services/index'));
const BlackBoxAI = lazy(() => import('../pages/cloud-services/BlackBoxAI'));

// Loading fallback
const LoadingFallback = (): JSX.Element => (
  <div className="flex items-center justify-center h-full min-h-[400px]">
    <div className="text-center">
      <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
      <p className="text-gray-600">Loading TechHub...</p>
    </div>
  </div>
);

// Placeholder component for missing features
const ComingSoonPlaceholder = ({ title }: { title: string }) => (
  <div className="flex items-center justify-center h-full min-h-[400px]">
    <div className="text-center">
      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <span className="text-2xl">🚀</span>
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">{title}</h2>
      <p className="text-gray-600 mb-4">This feature is coming soon!</p>
      <p className="text-sm text-gray-500">We're working hard to bring you this functionality.</p>
    </div>
  </div>
);

/**
 * Fixed TechHub Module Routes
 * Only imports existing components to prevent build errors
 */
const TechHubRoutesFixed = (): JSX.Element => {
  return (
    <Routes>
      {/* Index route - redirects to dashboard */}
      <Route
        index
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubDashboard />
            </Suspense>
          </TechHubLayout>
        }
      />

      {/* Dashboard route - explicit dashboard path */}
      <Route
        path="dashboard"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubDashboard />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      {/* Core TechHub Pages */}
      <Route
        path="settings"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubSettings />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="documentation"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubDocumentation />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="analytics"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubAnalytics />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="team"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubTeam />
            </Suspense>
          </TechHubLayout>
        }
      />

      {/* AI Personas */}
      <Route
        path="personas"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubPersonas />
            </Suspense>
          </TechHubLayout>
        }
      />

      {/* Technical Config */}
      <Route
        path="technical-config"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <TechHubTechnicalConfig />
            </Suspense>
          </TechHubLayout>
        }
      />

      {/* API Management */}
      <Route
        path="api-management"
        element={
          <TechHubLayout>
            <TechHubErrorBoundary>
              <Suspense fallback={<LoadingFallback />}>
                <ApiManagementPage />
              </Suspense>
            </TechHubErrorBoundary>
          </TechHubLayout>
        }
      />
      
      <Route
        path="api-management/tester"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <APITester />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="api-management/add"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <AddAPIEndpoint />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="api-management/providers"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <APIProviders />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="api-management/requesty"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <RequestyPage />
            </Suspense>
          </TechHubLayout>
        }
      />

      {/* Integrations */}
      <Route
        path="integrations"
        element={
          <TechHubLayout>
            <TechHubErrorBoundary>
              <Suspense fallback={<LoadingFallback />}>
                <IntegrationsPage />
              </Suspense>
            </TechHubErrorBoundary>
          </TechHubLayout>
        }
      />
      
      <Route
        path="integrations/*"
        element={
          <TechHubLayout>
            <TechHubErrorBoundary>
              <Suspense fallback={<LoadingFallback />}>
                <IntegrationsPage />
              </Suspense>
            </TechHubErrorBoundary>
          </TechHubLayout>
        }
      />

      {/* WooCommerce Integration Routes */}
      <Route
        path="integrations/woocommerce/staging"
        element={
          <TechHubLayout>
            <TechHubErrorBoundary>
              <Suspense fallback={<LoadingFallback />}>
                <WooCommerceStagingDashboard />
              </Suspense>
            </TechHubErrorBoundary>
          </TechHubLayout>
        }
      />

      <Route
        path="integrations/woocommerce/configuration"
        element={
          <TechHubLayout>
            <TechHubErrorBoundary>
              <Suspense fallback={<LoadingFallback />}>
                <WooCommerceConfiguration />
              </Suspense>
            </TechHubErrorBoundary>
          </TechHubLayout>
        }
      />

      {/* Cloud Services */}
      <Route
        path="cloud-services"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <CloudServicesIndex />
            </Suspense>
          </TechHubLayout>
        }
      />
      
      <Route
        path="cloud-services/blackbox-ai"
        element={
          <TechHubLayout>
            <Suspense fallback={<LoadingFallback />}>
              <BlackBoxAI />
            </Suspense>
          </TechHubLayout>
        }
      />

      {/* Placeholder routes for missing features */}
      <Route
        path="data-management/*"
        element={
          <TechHubLayout>
            <ComingSoonPlaceholder title="Data Management" />
          </TechHubLayout>
        }
      />
      
      <Route
        path="supplier-costing/*"
        element={
          <TechHubLayout>
            <ComingSoonPlaceholder title="Supplier Costing" />
          </TechHubLayout>
        }
      />

      {/* Catch all route - redirect to dashboard */}
      <Route path="*" element={<Navigate to="/dashboard/tech-hub/dashboard" replace />} />
    </Routes>
  );
};

export default TechHubRoutesFixed;
