import React, { Suspense } from 'react';
import { Routes, Route } from "react-router-dom";
import { PlatformLayout } from '@/components/layouts/PlatformLayout';
import { CalendarClock, Calendar, Plus, Clock, Bell } from 'lucide-react';
import { NavCategory } from '@/components/layout/sidebar/types';

// Lazy load components
const EventsPage = React.lazy(() => import("@/pages/auto/EventsPage"));

// Loading fallback component
const EventsLoadingFallback = () => (
  <div className="flex items-center justify-center h-64">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent"></div>
      <p className="text-sm text-gray-600">Loading Events Module...</p>
    </div>
  </div>
);

export const EventsNavCategories: NavCategory[] = [
  {
    name: "Events",
    label: "Events",
    items: [
      { label: "Dashboard", path: "/events", icon: CalendarClock },
      { label: "Calendar", path: "/events/calendar", icon: Calendar },
      { label: "Create Event", path: "/events/new", icon: Plus },
      { label: "Schedule", path: "/events/schedule", icon: Clock },
      { label: "Notifications", path: "/events/notifications", icon: Bell }
    ]
  }
];

export const EventsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route
        index
        element={
          <>
            <PlatformLayout moduleTitle="Events Management" useGlobalNavigation>
              <Suspense fallback={<EventsLoadingFallback />}>
                <EventsPage />
              </Suspense>
            </PlatformLayout>
          </>
        }
      />
      <Route
        path="dashboard"
        element={
          <>
            <PlatformLayout moduleTitle="Events Management" useGlobalNavigation>
              <Suspense fallback={<EventsLoadingFallback />}>
                <EventsPage />
              </Suspense>
            </PlatformLayout>
          </>
        }
      />
      <Route
        path="calendar"
        element={
          <>
            <PlatformLayout moduleTitle="Events Calendar" useGlobalNavigation>
              <Suspense fallback={<EventsLoadingFallback />}>
                <EventsPage />
              </Suspense>
            </PlatformLayout>
          </>
        }
      />
      <Route
        path="new"
        element={
          <>
            <PlatformLayout moduleTitle="Create Event" useGlobalNavigation>
              <Suspense fallback={<EventsLoadingFallback />}>
                <EventsPage />
              </Suspense>
            </PlatformLayout>
          </>
        }
      />
      <Route
        path="schedule"
        element={
          <>
            <PlatformLayout moduleTitle="Event Schedule" useGlobalNavigation>
              <Suspense fallback={<EventsLoadingFallback />}>
                <EventsPage />
              </Suspense>
            </PlatformLayout>
          </>
        }
      />
      <Route
        path="notifications"
        element={
          <>
            <PlatformLayout moduleTitle="Event Notifications" useGlobalNavigation>
              <Suspense fallback={<EventsLoadingFallback />}>
                <EventsPage />
              </Suspense>
            </PlatformLayout>
          </>
        }
      />
    </Routes>
  );
};
