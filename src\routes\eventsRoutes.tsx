import React from 'react';
import { Routes, Route } from "react-router-dom";

export const EventsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={
        <div className="container mx-auto py-6 px-4 max-w-7xl">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Events Management</h1>
            <p className="text-gray-600">Manage events, bookings, and schedules</p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg border hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-xl">📅</span>
                </div>
                <h3 className="font-semibold">Events Dashboard</h3>
              </div>
              <p className="text-sm text-gray-600">View all events and statistics</p>
            </div>

            <div className="bg-white p-6 rounded-lg border hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 text-xl">📆</span>
                </div>
                <h3 className="font-semibold">Calendar View</h3>
              </div>
              <p className="text-sm text-gray-600">View events in calendar format</p>
            </div>

            <div className="bg-white p-6 rounded-lg border hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600 text-xl">➕</span>
                </div>
                <h3 className="font-semibold">Create Event</h3>
              </div>
              <p className="text-sm text-gray-600">Create new events and bookings</p>
            </div>

            <div className="bg-white p-6 rounded-lg border hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-600 text-xl">🔔</span>
                </div>
                <h3 className="font-semibold">Notifications</h3>
              </div>
              <p className="text-sm text-gray-600">Manage event notifications</p>
            </div>
          </div>

          {/* Recent Events */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Events</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h3 className="font-medium">Annual Conference 2024</h3>
                  <p className="text-sm text-gray-600">March 15, 2024 - Conference Center</p>
                </div>
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">Active</span>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h3 className="font-medium">Product Launch Event</h3>
                  <p className="text-sm text-gray-600">March 20, 2024 - Main Auditorium</p>
                </div>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">Planned</span>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h3 className="font-medium">Team Building Workshop</h3>
                  <p className="text-sm text-gray-600">March 25, 2024 - Outdoor Venue</p>
                </div>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-sm">Draft</span>
              </div>
            </div>
          </div>
        </div>
      } />

      <Route path="dashboard" element={
        <div className="p-8">
          <h1 className="text-3xl font-bold">Events Dashboard</h1>
          <p>Events dashboard content goes here.</p>
        </div>
      } />

      <Route path="calendar" element={
        <div className="p-8">
          <h1 className="text-3xl font-bold">Events Calendar</h1>
          <p>Events calendar content goes here.</p>
        </div>
      } />

      <Route path="new" element={
        <div className="p-8">
          <h1 className="text-3xl font-bold">Create Event</h1>
          <p>Create new event form goes here.</p>
        </div>
      } />

      <Route path="schedule" element={
        <div className="p-8">
          <h1 className="text-3xl font-bold">Event Schedule</h1>
          <p>Event schedule management goes here.</p>
        </div>
      } />

      <Route path="notifications" element={
        <div className="p-8">
          <h1 className="text-3xl font-bold">Event Notifications</h1>
          <p>Event notifications management goes here.</p>
        </div>
      } />
    </Routes>
  );
};

export default EventsRoutes;
