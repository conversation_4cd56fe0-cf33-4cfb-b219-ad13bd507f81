import React from 'react';
import { Routes, Route } from "react-router-dom";
import { PlatformLayout } from '@/components/layouts/PlatformLayout';
import { CalendarClock, Calendar, Plus, Clock, Bell } from 'lucide-react';
import { NavCategory } from '@/components/layout/sidebar/types';

// Lazy load components
const EventsPage = React.lazy(() => import("@/pages/auto/EventsPage"));

export const EventsNavCategories: NavCategory[] = [
  {
    name: "Events",
    label: "Events",
    items: [
      { label: "Dashboard", path: "/events", icon: CalendarClock },
      { label: "Calendar", path: "/events/calendar", icon: Calendar },
      { label: "Create Event", path: "/events/new", icon: Plus },
      { label: "Schedule", path: "/events/schedule", icon: Clock },
      { label: "Notifications", path: "/events/notifications", icon: Bell }
    ]
  }
];

export const EventsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={
        <PlatformLayout moduleTitle="Events">
          <React.Suspense fallback={<div>Loading...</div>}>
            <EventsPage />
          </React.Suspense>
        </PlatformLayout>
      } />
      <Route path="calendar" element={
        <PlatformLayout moduleTitle="Events Calendar">
          <React.Suspense fallback={<div>Loading...</div>}>
            <EventsPage />
          </React.Suspense>
        </PlatformLayout>
      } />
      <Route path="new" element={
        <PlatformLayout moduleTitle="Create Event">
          <React.Suspense fallback={<div>Loading...</div>}>
            <EventsPage />
          </React.Suspense>
        </PlatformLayout>
      } />
      <Route path="schedule" element={
        <PlatformLayout moduleTitle="Event Schedule">
          <React.Suspense fallback={<div>Loading...</div>}>
            <EventsPage />
          </React.Suspense>
        </PlatformLayout>
      } />
      <Route path="notifications" element={
        <PlatformLayout moduleTitle="Event Notifications">
          <React.Suspense fallback={<div>Loading...</div>}>
            <EventsPage />
          </React.Suspense>
        </PlatformLayout>
      } />
    </Routes>
  );
};
