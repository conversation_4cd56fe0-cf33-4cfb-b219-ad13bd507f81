-- Ensure Integration Tables Exist
-- Creates missing tables for WooCommerce integration system

-- Create integration_configs table if it doesn't exist
CREATE TABLE IF NOT EXISTS integration_configs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    integration_type TEXT NOT NULL,
    config JSONB NOT NULL DEFAULT '{}',
    is_globally_enabled BOOLEAN DEFAULT true,
    last_known_status TEXT DEFAULT 'unknown',
    last_status_check_at TIMESTAMPTZ DEFAULT NOW(),
    last_successful_sync_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, integration_type)
);

-- Create woocommerce_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS woocommerce_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    organization_id TEXT NOT NULL UNIQUE,
    store_url TEXT NOT NULL,
    consumer_key TEXT NOT NULL,
    consumer_secret TEXT NOT NULL,
    webhook_secret TEXT,
    api_version TEXT DEFAULT 'v3',
    sync_enabled BOOLEAN DEFAULT true,
    batch_size INTEGER DEFAULT 100,
    sync_frequency_minutes INTEGER DEFAULT 15,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create woocommerce_customers_staging table if it doesn't exist
CREATE TABLE IF NOT EXISTS woocommerce_customers_staging (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    wc_customer_id INTEGER NOT NULL,
    organization_id TEXT NOT NULL,
    email TEXT,
    first_name TEXT,
    last_name TEXT,
    username TEXT,
    date_created TIMESTAMPTZ,
    date_modified TIMESTAMPTZ,
    billing_first_name TEXT,
    billing_last_name TEXT,
    billing_company TEXT,
    billing_address_1 TEXT,
    billing_address_2 TEXT,
    billing_city TEXT,
    billing_state TEXT,
    billing_postcode TEXT,
    billing_country TEXT,
    billing_email TEXT,
    billing_phone TEXT,
    shipping_first_name TEXT,
    shipping_last_name TEXT,
    shipping_company TEXT,
    shipping_address_1 TEXT,
    shipping_address_2 TEXT,
    shipping_city TEXT,
    shipping_state TEXT,
    shipping_postcode TEXT,
    shipping_country TEXT,
    is_paying_customer BOOLEAN DEFAULT false,
    avatar_url TEXT,
    meta_data JSONB DEFAULT '[]',
    raw_data JSONB NOT NULL,
    sync_status TEXT DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(organization_id, wc_customer_id)
);

-- Create woocommerce_products_staging table if it doesn't exist
CREATE TABLE IF NOT EXISTS woocommerce_products_staging (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    wc_product_id INTEGER NOT NULL,
    organization_id TEXT NOT NULL,
    name TEXT,
    slug TEXT,
    permalink TEXT,
    date_created TIMESTAMPTZ,
    date_modified TIMESTAMPTZ,
    type TEXT,
    status TEXT,
    featured BOOLEAN DEFAULT false,
    catalog_visibility TEXT,
    description TEXT,
    short_description TEXT,
    sku TEXT,
    price DECIMAL(10,2),
    regular_price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    currency TEXT DEFAULT 'ZAR',
    on_sale BOOLEAN DEFAULT false,
    purchasable BOOLEAN DEFAULT true,
    total_sales INTEGER DEFAULT 0,
    virtual BOOLEAN DEFAULT false,
    downloadable BOOLEAN DEFAULT false,
    manage_stock BOOLEAN DEFAULT false,
    stock_quantity INTEGER,
    stock_status TEXT,
    weight TEXT,
    dimensions JSONB DEFAULT '{}',
    categories JSONB DEFAULT '[]',
    tags JSONB DEFAULT '[]',
    images JSONB DEFAULT '[]',
    attributes JSONB DEFAULT '[]',
    meta_data JSONB DEFAULT '[]',
    raw_data JSONB NOT NULL,
    sync_status TEXT DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(organization_id, wc_product_id)
);

-- Create woocommerce_orders_staging table if it doesn't exist
CREATE TABLE IF NOT EXISTS woocommerce_orders_staging (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    wc_order_id INTEGER NOT NULL,
    organization_id TEXT NOT NULL,
    order_number TEXT,
    status TEXT,
    currency TEXT DEFAULT 'ZAR',
    total DECIMAL(10,2),
    subtotal DECIMAL(10,2),
    total_tax DECIMAL(10,2),
    customer_id INTEGER,
    billing_info JSONB DEFAULT '{}',
    shipping_info JSONB DEFAULT '{}',
    line_items JSONB DEFAULT '[]',
    date_created TIMESTAMPTZ,
    date_modified TIMESTAMPTZ,
    raw_data JSONB NOT NULL,
    sync_status TEXT DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(organization_id, wc_order_id)
);

-- Create woocommerce_sync_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS woocommerce_sync_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    organization_id TEXT NOT NULL,
    sync_type TEXT NOT NULL, -- 'customers', 'products', 'orders'
    sync_mode TEXT NOT NULL, -- 'batch', 'full', 'incremental'
    batch_size INTEGER,
    status TEXT DEFAULT 'running', -- 'running', 'completed', 'failed'
    records_processed INTEGER DEFAULT 0,
    records_success INTEGER DEFAULT 0,
    records_error INTEGER DEFAULT 0,
    records_skipped INTEGER DEFAULT 0,
    error_message TEXT,
    sync_details JSONB DEFAULT '{}',
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_integration_configs_user_type ON integration_configs(user_id, integration_type);
CREATE INDEX IF NOT EXISTS idx_wc_config_org_id ON woocommerce_config(organization_id);
CREATE INDEX IF NOT EXISTS idx_wc_customers_staging_org_id ON woocommerce_customers_staging(organization_id);
CREATE INDEX IF NOT EXISTS idx_wc_customers_staging_wc_id ON woocommerce_customers_staging(wc_customer_id);
CREATE INDEX IF NOT EXISTS idx_wc_customers_staging_sync_status ON woocommerce_customers_staging(sync_status);
CREATE INDEX IF NOT EXISTS idx_wc_products_staging_org_id ON woocommerce_products_staging(organization_id);
CREATE INDEX IF NOT EXISTS idx_wc_products_staging_wc_id ON woocommerce_products_staging(wc_product_id);
CREATE INDEX IF NOT EXISTS idx_wc_products_staging_sync_status ON woocommerce_products_staging(sync_status);
CREATE INDEX IF NOT EXISTS idx_wc_orders_staging_org_id ON woocommerce_orders_staging(organization_id);
CREATE INDEX IF NOT EXISTS idx_wc_sync_log_org_id ON woocommerce_sync_log(organization_id);
CREATE INDEX IF NOT EXISTS idx_wc_sync_log_status ON woocommerce_sync_log(status);

-- Enable RLS on all tables
ALTER TABLE integration_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE woocommerce_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE woocommerce_customers_staging ENABLE ROW LEVEL SECURITY;
ALTER TABLE woocommerce_products_staging ENABLE ROW LEVEL SECURITY;
ALTER TABLE woocommerce_orders_staging ENABLE ROW LEVEL SECURITY;
ALTER TABLE woocommerce_sync_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Integration configs - users can only access their own configs
DROP POLICY IF EXISTS "Users can manage their own integration configs" ON integration_configs;
CREATE POLICY "Users can manage their own integration configs" ON integration_configs
    FOR ALL USING (auth.uid() = user_id);

-- WooCommerce config - users can only access their own configs
DROP POLICY IF EXISTS "Users can manage their own WooCommerce config" ON woocommerce_config;
CREATE POLICY "Users can manage their own WooCommerce config" ON woocommerce_config
    FOR ALL USING (auth.uid()::text = organization_id);

-- WooCommerce staging tables - users can only access their own data
DROP POLICY IF EXISTS "Users can manage their own customer staging data" ON woocommerce_customers_staging;
CREATE POLICY "Users can manage their own customer staging data" ON woocommerce_customers_staging
    FOR ALL USING (auth.uid()::text = organization_id);

DROP POLICY IF EXISTS "Users can manage their own product staging data" ON woocommerce_products_staging;
CREATE POLICY "Users can manage their own product staging data" ON woocommerce_products_staging
    FOR ALL USING (auth.uid()::text = organization_id);

DROP POLICY IF EXISTS "Users can manage their own order staging data" ON woocommerce_orders_staging;
CREATE POLICY "Users can manage their own order staging data" ON woocommerce_orders_staging
    FOR ALL USING (auth.uid()::text = organization_id);

DROP POLICY IF EXISTS "Users can manage their own sync logs" ON woocommerce_sync_log;
CREATE POLICY "Users can manage their own sync logs" ON woocommerce_sync_log
    FOR ALL USING (auth.uid()::text = organization_id);

-- Grant necessary permissions
GRANT ALL ON integration_configs TO authenticated;
GRANT ALL ON woocommerce_config TO authenticated;
GRANT ALL ON woocommerce_customers_staging TO authenticated;
GRANT ALL ON woocommerce_products_staging TO authenticated;
GRANT ALL ON woocommerce_orders_staging TO authenticated;
GRANT ALL ON woocommerce_sync_log TO authenticated;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_integration_configs_updated_at ON integration_configs;
CREATE TRIGGER update_integration_configs_updated_at
    BEFORE UPDATE ON integration_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_woocommerce_config_updated_at ON woocommerce_config;
CREATE TRIGGER update_woocommerce_config_updated_at
    BEFORE UPDATE ON woocommerce_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_wc_customers_staging_updated_at ON woocommerce_customers_staging;
CREATE TRIGGER update_wc_customers_staging_updated_at
    BEFORE UPDATE ON woocommerce_customers_staging
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_wc_products_staging_updated_at ON woocommerce_products_staging;
CREATE TRIGGER update_wc_products_staging_updated_at
    BEFORE UPDATE ON woocommerce_products_staging
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ All WooCommerce integration tables created successfully!';
    RAISE NOTICE '✅ RLS policies enabled for security';
    RAISE NOTICE '✅ Indexes created for performance';
    RAISE NOTICE '✅ Triggers created for automatic timestamps';
    RAISE NOTICE '🚀 WooCommerce integration system ready for use!';
END $$;
