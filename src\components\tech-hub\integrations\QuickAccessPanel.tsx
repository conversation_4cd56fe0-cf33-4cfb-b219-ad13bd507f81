/**
 * Quick Access Panel for WooCommerce Staging System
 * Provides prominent access to staging functionality
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  Settings, 
  Download, 
  ArrowRight,
  Zap,
  CheckCircle2,
  AlertTriangle
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export function QuickAccessPanel() {
  const navigate = useNavigate();

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Zap className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-xl text-blue-900">WooCommerce Staging System</CardTitle>
              <CardDescription className="text-blue-700">
                Real-time data synchronization with validation staging area
              </CardDescription>
            </div>
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <CheckCircle2 className="w-3 h-3 mr-1" />
            Ready
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* Staging Dashboard */}
          <div className="bg-white rounded-lg p-4 border border-blue-100 hover:border-blue-200 transition-colors">
            <div className="flex items-center gap-3 mb-3">
              <Database className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Staging Dashboard</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Monitor real-time sync progress, validate data, and manage staging area
            </p>
            <Button 
              size="sm" 
              className="w-full"
              onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/staging')}
            >
              Open Dashboard
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>

          {/* Configuration */}
          <div className="bg-white rounded-lg p-4 border border-blue-100 hover:border-blue-200 transition-colors">
            <div className="flex items-center gap-3 mb-3">
              <Settings className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-gray-900">Configuration</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Set up API credentials, sync settings, and batch processing options
            </p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full"
              onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/configuration')}
            >
              Configure API
              <Settings className="w-4 h-4 ml-2" />
            </Button>
          </div>

          {/* Batch Processing */}
          <div className="bg-white rounded-lg p-4 border border-blue-100 hover:border-blue-200 transition-colors">
            <div className="flex items-center gap-3 mb-3">
              <Download className="h-5 w-5 text-purple-600" />
              <h3 className="font-semibold text-gray-900">Batch Processing</h3>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Download customers and products in batches for validation
            </p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full"
              onClick={() => navigate('/dashboard/tech-hub/integrations/woocommerce/staging')}
            >
              Start Batch Sync
              <Download className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* Key Features */}
        <div className="bg-white rounded-lg p-4 border border-blue-100">
          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            Key Features
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Real-time sync</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Batch processing</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>Data validation</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span>ZAR currency</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
          <div className="flex items-center gap-2 text-blue-800 mb-2">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium text-sm">Setup Required</span>
          </div>
          <p className="text-sm text-blue-700">
            Configure your WooCommerce API connection to start using the staging system.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
