[{"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_audit_trigger\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_audit_trigger", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_audit_trigger_1c864dc39924a6635f488244882a011b"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_time_logs_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_time_logs_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_time_logs_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.increment_asset_download_count\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "increment_asset_download_count", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_increment_asset_download_count_ec1842b5124092cb8721bab577f1929f"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_financial_dashboard_metrics\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_financial_dashboard_metrics", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_financial_dashboard_metrics_cd5655e75420a4cbd8b4c397f6516fb3"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_stock_levels\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_stock_levels", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_stock_levels_da0128455156c7426fe60d9c66511043"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.can_manage_brand\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "can_manage_brand", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_can_manage_brand_527f914e949bef05860212bf965fb9e9"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_credit_availability\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_credit_availability", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_credit_availability_0238c73ebc7d4ad3d46b75a3dec2d084"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.safe_apply_admin_fix\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "safe_apply_admin_fix", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_safe_apply_admin_fix_ea4fdf5966b14dd52b585474cadebd1c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_complete_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_complete_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_complete_user_b576779e5a3916e7b4cca80479c37ac4"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_user_preferences_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_user_preferences_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_user_preferences_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_rental_bookings_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_rental_bookings_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_rental_bookings_updated_at_06bcf30ac3d0a7f279a54cbf228a7bec"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_load_in_procedures_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_load_in_procedures_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_load_in_procedures_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.has_hr_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "has_hr_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_has_hr_permission_d2d9d48f21cd2a802a16148039539e03"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.handle_new_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "handle_new_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_handle_new_user_174f8c2ab55e67a1fde951cee56d111e"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_brand_management_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_brand_management_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_brand_management_permission_1f932a3670b1e5fbea2d1b7a12726da0"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.user_has_repair_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "user_has_repair_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_user_has_repair_role_b6fa0f8746d33ff39a165bdee8562021"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_manager_hierarchy\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_manager_hierarchy", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_manager_hierarchy_51917a508f37059f1892725539bc5ab7"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_invoice_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_invoice_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_invoice_number_f2a2c3f98bbe59d2617332b70bfe63a1"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_financial_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_financial_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_financial_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_campaign_status\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_campaign_status", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_campaign_status_29aa33de913383f5ce0f7326e6eaa3a9"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_organization_employee_count\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_organization_employee_count", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_organization_employee_count_59a3fe6a3b444b438b64ce2e81f30100"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.user_has_inventory_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "user_has_inventory_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_user_has_inventory_role_0fd74202ce9750780fc9ad21d8d28178"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_journal_entry_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_journal_entry_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_journal_entry_number_df5e13c413e5117b4cef2324a0099265"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_guideline_review_date\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_guideline_review_date", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_guideline_review_date_592e5f975fc562d4e673e3ea3a55f0ad"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_lead_last_touch\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_lead_last_touch", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_lead_last_touch_e77a4504a5de8b6b72f20e4113316e2b"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_updated_at_column\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_updated_at_column", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_updated_at_column_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_enhanced_user_with_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_enhanced_user_with_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_enhanced_user_with_role_f4b8552de3b0588c8f04ecee310be445"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.sync_user_roles\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "sync_user_roles", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_sync_user_roles_7dcd5e35eb14642dff2b7c77b857eb8c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_customer_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_customer_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_customer_number_f9fb64018da2d8f8a0ce7b193bfdb43e"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_rental_invoices_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_rental_invoices_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_rental_invoices_updated_at_06bcf30ac3d0a7f279a54cbf228a7bec"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_modules\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_modules", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_modules_7767db6fa35efdc9041ac4ee6980f45c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.is_admin_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "is_admin_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_is_admin_user_072d77e394fbb23dae91bc252ab92eb6"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_audit_log\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_audit_log", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_audit_log_7c23bbff618d07ce8c897fa4b73f5232"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.setup_admin_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "setup_admin_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_setup_admin_user_305ef8f3c1dd1ae21e6df954b5bb9654"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_crew_assignments_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_crew_assignments_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_crew_assignments_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_payment_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_payment_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_payment_number_5511682544ae825e9a02cddc4d440788"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.assign_brand_management_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "assign_brand_management_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_assign_brand_management_role_787bfb90efecef8da52797a107ed30ca"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_profiles_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_profiles_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_profiles_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_crew_certifications_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_crew_certifications_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_crew_certifications_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_customer_balance\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_customer_balance", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_customer_balance_b6290fc184aaa0f408d29cb9745cec9b"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_crew_members_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_crew_members_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_crew_members_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`rbac.check_business_governance_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_business_governance_permission", "type": "function", "schema": "rbac"}, "cache_key": "function_search_path_mutable_rbac_check_business_governance_permission_6235dc9e95e3ed6438c584898e909ee2"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_overdue_contracts\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_overdue_contracts", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_overdue_contracts_97364ddc1ab3150f3705ba48e07e5b73"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_low_stock_items\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_low_stock_items", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_low_stock_items_08ba3d575bbe34ca9de94417169e6800"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.user_has_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "user_has_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_user_has_permission_c9e113a3cb45b821fed590275b5b9093"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_user_with_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_user_with_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_user_with_role_e927463492b2a087b93b67f1efbfce19"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_customers_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_customers_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_customers_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_governance_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_governance_permissions", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_governance_permissions_c700cc05331c438e837a77c0233d5726"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_marketing_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_marketing_permissions", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_marketing_permissions_9c345e3f95fc67f6d87603a79ad0eaf4"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_integration_configs_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_integration_configs_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_integration_configs_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_repair_templates_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_repair_templates_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_repair_templates_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.calculate_campaign_roi\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "calculate_campaign_roi", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_calculate_campaign_roi_3b98b12d7267501ac8f63d75fa35e7f7"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_permissions", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_permissions_0944d5c70d7e4d76dba2d4860b78ab1c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_booking_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_booking_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_booking_number_7bc889e068b9f6b62f3d938ac81a7ca4"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_department_hierarchy\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_department_hierarchy", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_department_hierarchy_a7953565b1d712cd45ac3ae72cb260c8"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_business_governance_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_business_governance_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_business_governance_permission_5cfa0de1f47cb68e1d4c07403aef38b2"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_permissions", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_permissions_6c73686c7c5c670dfb59c39f18d23dba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.user_has_module_access\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "user_has_module_access", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_user_has_module_access_6d6b513b3b1bd1d43c74051b7cdc173c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_organization\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_organization", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_organization_bdfd53d27c04d38dbcc596b76b249dc3"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_event_projects_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_event_projects_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_event_projects_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.validate_brand_asset_file\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "validate_brand_asset_file", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_validate_brand_asset_file_986bbf446d98db21924f4f43b46f78a6"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.setup_enhanced_admin_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "setup_enhanced_admin_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_setup_enhanced_admin_user_dd0feed6d14de355bfe8f0c0bd3ce34c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_rental_rates_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_rental_rates_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_rental_rates_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.calculate_rental_cost\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "calculate_rental_cost", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_calculate_rental_cost_96ba443a6c676e8f7ca28107996dcd1a"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.calculate_booking_estimate\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "calculate_booking_estimate", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_calculate_booking_estimate_bc0c7a43cafbcf255c06d016cfeab808"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`rbac.get_user_governance_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_governance_permissions", "type": "function", "schema": "rbac"}, "cache_key": "function_search_path_mutable_rbac_get_user_governance_permissions_e071341da6b15254636f7cbf3eb8d0fd"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_parts_inventory_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_parts_inventory_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_parts_inventory_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_user_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_user_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_user_role_7904c4c76ba0125bfa9c16ff1cb0546d"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_work_order_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_work_order_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_work_order_number_c94e8ae79e14909d1b63b4d2f2b0e1c8"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_permissions", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_permissions_95de7570e58b14b87eeb04561826a6a6"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_accessible_modules\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_accessible_modules", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_accessible_modules_525d5d69d946cd80a276639dbd5ad322"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_equipment_items_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_equipment_items_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_equipment_items_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_venue_specifications_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_venue_specifications_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_venue_specifications_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_venues_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_venues_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_venues_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_assignment_tasks_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_assignment_tasks_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_assignment_tasks_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_marketing_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_marketing_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_marketing_permission_1f932a3670b1e5fbea2d1b7a12726da0"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.audit_brand_changes\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "audit_brand_changes", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_audit_brand_changes_39a18e533bd247d55bb30ea2ada54160"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_contract_number\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_contract_number", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_contract_number_54758a36b3c4a8504bf2771b05736cbc"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.enhanced_user_has_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "enhanced_user_has_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_enhanced_user_has_permission_60462f278cc11ae2a2bf968872778336"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_rental_contracts_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_rental_contracts_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_rental_contracts_updated_at_06bcf30ac3d0a7f279a54cbf228a7bec"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_brand_permissions\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_brand_permissions", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_brand_permissions_513cc1a8f82c6406888c46940d49e25c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.is_super_admin_user\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "is_super_admin_user", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_is_super_admin_user_ea08899d4be2ba561267fb6f15753b09"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_current_rental_rate\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_current_rental_rate", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_current_rental_rate_f714bf1c4af1c965e768583870b8f6d0"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_asset_tag\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_asset_tag", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_asset_tag_8d07206440c90a6154ea10b2ed2654dc"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.enhanced_user_has_module_access\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "enhanced_user_has_module_access", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_enhanced_user_has_module_access_9d7fee4e614b968df67fa6d994d25ff7"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_repair_work_orders_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_repair_work_orders_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_repair_work_orders_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_equipment_catalog_updated_at\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_equipment_catalog_updated_at", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_equipment_catalog_updated_at_ef6b2d76360a727c9d6479352655b7ba"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.user_has_permission\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "user_has_permission", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_user_has_permission_ab88855d5bb0ba022101ddd958e7af78"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_pending_bookings\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_pending_bookings", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_pending_bookings_570d7a2c782c28c18a88db071f315da7"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.check_position_hierarchy\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "check_position_hierarchy", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_check_position_hierarchy_94861d856fa3de77aabc51a0e3d194a1"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.user_has_rental_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "user_has_rental_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_user_has_rental_role_4e66c9846d202cb0472e7f2128c80d6d"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.toggle_user_status\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "toggle_user_status", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_toggle_user_status_c8db08b1668d17fe46117157879e0851"}, {"name": "auth_otp_long_expiry", "title": "Auth OTP long expiry", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "OTP expiry exceeds recommended threshold", "detail": "We have detected that you have enabled the email provider with the OTP expiry set to more than an hour. It is recommended to set this value to less than an hour.", "cache_key": "auth_otp_long_expiry", "remediation": "https://supabase.com/docs/guides/platform/going-into-prod#security", "metadata": {"type": "auth", "entity": "<PERSON><PERSON>"}}, {"name": "auth_leaked_password_protection", "title": "Leaked Password Protection Disabled", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Leaked password protection is currently disabled.", "detail": "Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security.", "cache_key": "auth_leaked_password_protection", "remediation": "https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection", "metadata": {"type": "auth", "entity": "<PERSON><PERSON>"}}]