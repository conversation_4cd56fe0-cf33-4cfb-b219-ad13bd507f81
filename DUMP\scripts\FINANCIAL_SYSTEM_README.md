# QuantaSori Financial & Accounting System

## 🏦 Production-Ready Finance & Accounting System

A comprehensive, enterprise-grade financial and accounting system built with React, TypeScript, and Supabase. This system provides complete financial management capabilities including chart of accounts, journal entries, accounts receivable/payable, payments, and financial reporting.

## 📋 Table of Contents

- [Features](#features)
- [Architecture](#architecture)
- [Installation](#installation)
- [Database Setup](#database-setup)
- [Testing](#testing)
- [Deployment](#deployment)
- [Monitoring](#monitoring)
- [API Documentation](#api-documentation)
- [Contributing](#contributing)

## ✨ Features

### Core Financial Features
- **Chart of Accounts Management** - Complete account hierarchy with auto-generated codes
- **Journal Entries** - Double-entry bookkeeping with validation
- **General Ledger** - Real-time transaction tracking
- **Accounts Receivable** - Customer invoice management
- **Accounts Payable** - Vendor bill management
- **Payment Processing** - Multi-method payment handling
- **Bank Account Management** - Cash flow tracking
- **Tax Code Management** - Configurable tax calculations

### Financial Reporting
- **Financial Dashboard** - Real-time metrics and KPIs
- **Trial Balance** - Account balance verification
- **Income Statement** - Profit & loss reporting
- **Balance Sheet** - Financial position reporting
- **Cash Flow Statement** - Cash movement analysis
- **Custom Reports** - Flexible reporting engine

### Security & Compliance
- **Row Level Security (RLS)** - Organization-based data isolation
- **Audit Trails** - Complete transaction history
- **User Permissions** - Role-based access control
- **Data Validation** - Comprehensive input validation
- **Backup & Recovery** - Automated data protection

## 🏗️ Architecture

### Frontend Stack
- **React 18** - Modern UI framework
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Radix UI** - Accessible component library
- **React Query** - Server state management
- **React Hook Form** - Form management
- **Zustand** - Client state management

### Backend Stack
- **Supabase** - Backend-as-a-Service
- **PostgreSQL** - Relational database
- **Row Level Security** - Data isolation
- **Real-time Subscriptions** - Live updates
- **Edge Functions** - Serverless compute

### Development Tools
- **Vite** - Fast build tool
- **Jest** - Testing framework
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

## 🚀 Installation

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account
- Git

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd quantasori-financial-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your Supabase credentials
   ```

4. **Database setup**
   ```bash
   npm run setup-db
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

## 🗄️ Database Setup

### Automated Setup
```bash
# Complete database setup with sample data
npm run setup-db

# Apply RLS policies
psql -f fix-profiles-rls.sql

# Apply financial functions
psql -f financial-database-functions.sql
```

### Manual Setup
1. Create Supabase project
2. Run migration files in order:
   - `supabase/migrations/0000_init_unified_schema.sql`
   - `fix-profiles-rls.sql`
   - `financial-database-functions.sql`

### Database Schema
- **organizations** - Multi-tenant organization data
- **chart_of_accounts** - Account structure
- **general_ledger** - Transaction records
- **journal_entries** - Journal entry headers
- **journal_entry_lines** - Journal entry details
- **accounts_receivable** - Customer invoices
- **accounts_payable** - Vendor bills
- **payments** - Payment records
- **bank_accounts** - Bank account management

## 🧪 Testing

### Test Suite
```bash
# Run all tests
npm run test:financial

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:e2e

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Test Structure
- **Unit Tests** - Component and service testing
- **Integration Tests** - API and database testing
- **E2E Tests** - Full user workflow testing
- **Coverage Reports** - Code coverage analysis

### Test Files
- `src/tests/financial/accountService.test.ts`
- `src/tests/financial/FinancialDashboard.test.tsx`
- `src/tests/setup.ts` - Test configuration

## 🚀 Deployment

### Automated Deployment
```bash
# Complete deployment pipeline
npm run deploy
```

### Manual Deployment
```bash
# Build application
npm run build

# Run tests
npm run test:financial

# Deploy to production
# (Configure your deployment target)
```

### Deployment Features
- **Automated Testing** - Pre-deployment validation
- **Database Migrations** - Schema updates
- **Health Checks** - Post-deployment verification
- **Rollback Support** - Quick recovery
- **Performance Optimization** - Build optimization

## 📊 Monitoring

### Health Monitoring
```bash
# Start monitoring service
npm run monitor

# Check system health
npm run health-check

# View system status
node scripts/monitor-financial-system.js status
```

### Monitoring Features
- **Real-time Health Checks** - System status monitoring
- **Performance Metrics** - Response time tracking
- **Error Tracking** - Exception monitoring
- **Resource Usage** - CPU, memory, disk monitoring
- **Alert System** - Automated notifications

### Logs & Analytics
- **Application Logs** - Structured logging
- **Audit Trails** - Financial transaction tracking
- **Performance Logs** - System performance data
- **Error Logs** - Exception tracking

## 📚 API Documentation

### Financial Services

#### AccountService
```typescript
// Get all accounts
const accounts = await AccountService.getAccounts(organizationId);

// Create account
const account = await AccountService.createAccount(accountData, organizationId);

// Get account balance
const balance = await AccountService.getAccountBalance(accountId);
```

#### JournalService
```typescript
// Create journal entry
const entry = await JournalService.createJournalEntry(entryData);

// Post journal entry
await JournalService.postJournalEntry(entryId, userId);
```

#### ReportService
```typescript
// Get financial metrics
const metrics = await ReportService.getFinancialMetrics(organizationId);

// Generate trial balance
const trialBalance = await ReportService.getTrialBalance(organizationId);
```

### Database Functions
- `generate_account_code()` - Auto-generate account codes
- `get_account_balance()` - Calculate account balances
- `get_trial_balance()` - Generate trial balance
- `get_financial_dashboard_metrics()` - Dashboard metrics

## 🔧 Configuration

### Environment Variables
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_APP_NAME=QuantaSori Financial System
VITE_APP_VERSION=2.0.0
```

### Feature Flags
- Multi-currency support
- Advanced reporting
- Real-time notifications
- Audit logging

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch
3. Make changes
4. Run tests
5. Submit pull request

### Code Standards
- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Jest testing
- Conventional commits

### Pull Request Process
1. Update documentation
2. Add/update tests
3. Ensure CI passes
4. Request review
5. Merge after approval

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Documentation
- [API Reference](./docs/api.md)
- [Database Schema](./docs/schema.md)
- [Deployment Guide](./docs/deployment.md)

### Community
- GitHub Issues
- Discord Community
- Email Support

### Professional Support
- Enterprise Support
- Custom Development
- Training Services

---

**Built with ❤️ by the QuantaSori Team**
