-- Database Validation Script for Production Deployment
-- Validates all required fields and ensures data integrity

-- Check if all required tables exist
DO $$
DECLARE
    missing_tables text[] := ARRAY[]::text[];
    table_name text;
    required_tables text[] := ARRAY[
        'organizations',
        'customers',
        'vendors',
        'equipment_catalog',
        'equipment_items',
        'purchase_orders',
        'woocommerce_config',
        'woocommerce_customers_staging',
        'woocommerce_products_staging',
        'woocommerce_orders_staging',
        'woocommerce_sync_log'
    ];
BEGIN
    FOREACH table_name IN ARRAY required_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = table_name
        ) THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Missing required tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'All required tables exist ✓';
    END IF;
END $$;

-- Validate customers table structure
DO $$
DECLARE
    missing_columns text[] := ARRAY[]::text[];
    column_name text;
    required_columns text[] := ARRAY[
        'id', 'organization_id', 'customer_type', 'contact_name', 'email',
        'phone', 'address', 'city', 'state_province', 'postal_code', 'country',
        'credit_status', 'credit_limit', 'current_balance', 'is_active'
    ];
BEGIN
    FOREACH column_name IN ARRAY required_columns
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'customers' 
            AND column_name = column_name
        ) THEN
            missing_columns := array_append(missing_columns, column_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Missing columns in customers table: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'Customers table structure valid ✓';
    END IF;
END $$;

-- Validate equipment_catalog table structure
DO $$
DECLARE
    missing_columns text[] := ARRAY[]::text[];
    column_name text;
    required_columns text[] := ARRAY[
        'id', 'organization_id', 'category', 'brand', 'model', 'sku',
        'description', 'specifications', 'condition_rating', 'is_active'
    ];
BEGIN
    FOREACH column_name IN ARRAY required_columns
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'equipment_catalog' 
            AND column_name = column_name
        ) THEN
            missing_columns := array_append(missing_columns, column_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Missing columns in equipment_catalog table: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'Equipment catalog table structure valid ✓';
    END IF;
END $$;

-- Validate WooCommerce staging tables
DO $$
DECLARE
    missing_columns text[] := ARRAY[]::text[];
    column_name text;
    required_columns text[] := ARRAY[
        'id', 'wc_customer_id', 'organization_id', 'email', 'first_name', 'last_name',
        'billing_address_1', 'billing_city', 'billing_country', 'sync_status', 'raw_data'
    ];
BEGIN
    FOREACH column_name IN ARRAY required_columns
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'woocommerce_customers_staging' 
            AND column_name = column_name
        ) THEN
            missing_columns := array_append(missing_columns, column_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Missing columns in woocommerce_customers_staging table: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'WooCommerce customers staging table structure valid ✓';
    END IF;
END $$;

-- Check for proper indexes
DO $$
DECLARE
    missing_indexes text[] := ARRAY[]::text[];
    index_name text;
    required_indexes text[] := ARRAY[
        'idx_wc_customers_staging_org_id',
        'idx_wc_customers_staging_wc_id',
        'idx_wc_products_staging_org_id',
        'idx_wc_products_staging_wc_id'
    ];
BEGIN
    FOREACH index_name IN ARRAY required_indexes
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE schemaname = 'public' AND indexname = index_name
        ) THEN
            missing_indexes := array_append(missing_indexes, index_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_indexes, 1) > 0 THEN
        RAISE WARNING 'Missing indexes (performance may be affected): %', array_to_string(missing_indexes, ', ');
    ELSE
        RAISE NOTICE 'All required indexes exist ✓';
    END IF;
END $$;

-- Validate RLS policies are enabled
DO $$
DECLARE
    tables_without_rls text[] := ARRAY[]::text[];
    table_name text;
    rls_tables text[] := ARRAY[
        'customers',
        'equipment_catalog',
        'woocommerce_config',
        'woocommerce_customers_staging',
        'woocommerce_products_staging'
    ];
BEGIN
    FOREACH table_name IN ARRAY rls_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename = table_name 
            AND rowsecurity = true
        ) THEN
            tables_without_rls := array_append(tables_without_rls, table_name);
        END IF;
    END LOOP;
    
    IF array_length(tables_without_rls, 1) > 0 THEN
        RAISE WARNING 'Tables without RLS enabled (security risk): %', array_to_string(tables_without_rls, ', ');
    ELSE
        RAISE NOTICE 'All security tables have RLS enabled ✓';
    END IF;
END $$;

-- Check currency defaults are set to ZAR
DO $$
DECLARE
    non_zar_defaults text[] := ARRAY[]::text[];
    table_column text;
    currency_columns text[] := ARRAY[
        'marketing_settings.default_currency',
        'vendors.currency',
        'purchase_orders.currency'
    ];
BEGIN
    -- Check marketing_settings default
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'marketing_settings' 
        AND column_name = 'default_currency'
        AND column_default != '''ZAR''::character varying'
    ) THEN
        non_zar_defaults := array_append(non_zar_defaults, 'marketing_settings.default_currency');
    END IF;
    
    IF array_length(non_zar_defaults, 1) > 0 THEN
        RAISE WARNING 'Currency defaults not set to ZAR: %', array_to_string(non_zar_defaults, ', ');
    ELSE
        RAISE NOTICE 'Currency defaults properly set to ZAR ✓';
    END IF;
END $$;

-- Validate sample data exists
DO $$
DECLARE
    empty_tables text[] := ARRAY[]::text[];
    table_name text;
    row_count integer;
    data_tables text[] := ARRAY[
        'organizations'
    ];
BEGIN
    FOREACH table_name IN ARRAY data_tables
    LOOP
        EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO row_count;
        IF row_count = 0 THEN
            empty_tables := array_append(empty_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(empty_tables, 1) > 0 THEN
        RAISE WARNING 'Tables with no data (may need seeding): %', array_to_string(empty_tables, ', ');
    ELSE
        RAISE NOTICE 'Required tables have data ✓';
    END IF;
END $$;

-- Check for foreign key constraints
DO $$
DECLARE
    missing_fks text[] := ARRAY[]::text[];
    constraint_name text;
    required_fks text[] := ARRAY[
        'customers_organization_id_fkey',
        'equipment_catalog_organization_id_fkey',
        'woocommerce_customers_staging_organization_id_fkey'
    ];
BEGIN
    FOREACH constraint_name IN ARRAY required_fks
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_schema = 'public' 
            AND constraint_name = constraint_name
            AND constraint_type = 'FOREIGN KEY'
        ) THEN
            missing_fks := array_append(missing_fks, constraint_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_fks, 1) > 0 THEN
        RAISE WARNING 'Missing foreign key constraints: %', array_to_string(missing_fks, ', ');
    ELSE
        RAISE NOTICE 'All required foreign keys exist ✓';
    END IF;
END $$;

-- Final validation summary
DO $$
BEGIN
    RAISE NOTICE '=== DATABASE VALIDATION COMPLETE ===';
    RAISE NOTICE 'Database structure validated for production deployment';
    RAISE NOTICE 'WooCommerce staging system ready';
    RAISE NOTICE 'Currency system configured for ZAR';
    RAISE NOTICE 'Security policies enabled';
    RAISE NOTICE '====================================';
END $$;
