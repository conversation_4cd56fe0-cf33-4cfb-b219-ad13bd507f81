#!/usr/bin/env node

/**
 * 🛒 WOOCOMMERCE INTEGRATION MODULE EXTRACTOR
 * 
 * This script extracts the complete WooCommerce integration module
 * from the main QuantaSori platform according to the MODULE_EXTRACTION_PLAN.md
 * 
 * LIGHTS, CAMERA, ACTION! 🎬
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination paths
const SOURCE_DIR = path.resolve(__dirname, '../');
const DEST_DIR = path.resolve(__dirname, '../../DOCUECHANGE/modules/woocommerce-integration');

// Module configuration
const MODULE_CONFIG = {
  name: 'WooCommerce Integration',
  version: '1.0.0',
  description: 'Complete WooCommerce e-commerce integration with real-time sync capabilities',
  dependencies: [
    '@supabase/supabase-js',
    '@tanstack/react-query',
    'react',
    'react-dom',
    'react-router-dom',
    'lucide-react',
    'clsx',
    'tailwind-merge',
    'sonner',
    'axios'
  ]
};

// Files and directories to extract for WooCommerce
const EXTRACTION_MAP = {
  // WooCommerce Integration Components
  'src/components/integrations/woocommerce': 'src/components/woocommerce',
  
  // WooCommerce Pages (if they exist)
  'src/pages/integrations': 'src/pages/integrations',
  'src/pages/IntegrationsPage.tsx': 'src/pages/IntegrationsPage.tsx',
  
  // WooCommerce Services and Hooks
  'src/hooks/use-products.ts': 'src/hooks/use-products.ts',
  'src/hooks/useCustomerManagement.ts': 'src/hooks/useCustomerManagement.ts',
  
  // Customer Management
  'src/components/customers': 'src/components/customers',
  'src/components/customer-analytics': 'src/components/customer-analytics',
  'src/components/customer-communication': 'src/components/customer-communication',
  'src/pages/customer-management': 'src/pages/customer-management',
  'src/pages/customer-hub': 'src/pages/customer-hub',
  'src/features/customers': 'src/features/customers',
  'src/services/customer-analytics': 'src/services/customer-analytics',
  'src/services/customer-communication': 'src/services/customer-communication',
  
  // Product Management
  'src/pages/products': 'src/pages/products',
  'src/types/product.ts': 'src/types/product.ts',
  
  // Integration Routes
  'src/routes/customerManagementRoutes.tsx': 'src/routes/customerManagementRoutes.tsx',
  'src/routes/customerHubRoutes.tsx': 'src/routes/customerHubRoutes.tsx',
  'src/routes/productsRoutes.tsx': 'src/routes/productsRoutes.tsx',
  
  // Supabase Edge Functions for WooCommerce
  'supabase/functions': 'supabase/functions',
  
  // Database Migrations
  'supabase/migrations': 'supabase/migrations',
  
  // Shared Components
  'src/components/ui': 'src/components/ui',
  'src/components/common': 'src/components/common',
  'src/components/shared': 'src/components/shared',
  
  // Utilities
  'src/lib/utils.ts': 'src/lib/utils.ts',
  'src/integrations/supabase': 'src/integrations/supabase',
  
  // Styles
  'src/styles': 'src/styles',
  
  // Configuration files
  'tailwind.config.js': 'tailwind.config.js',
  'postcss.config.js': 'postcss.config.js',
  'tsconfig.json': 'tsconfig.json',
  'vite.config.ts': 'vite.config.ts',
  'components.json': 'components.json'
};

async function createModuleStructure() {
  console.log('🎬 LIGHTS, CAMERA, ACTION! Starting WooCommerce Module Extraction...');
  
  try {
    // Ensure destination directory exists
    await fs.ensureDir(DEST_DIR);
    
    // Create module package.json
    const packageJson = {
      name: '@quantasori/woocommerce-integration',
      version: MODULE_CONFIG.version,
      description: MODULE_CONFIG.description,
      main: 'src/index.ts',
      type: 'module',
      scripts: {
        dev: 'vite',
        build: 'tsc && vite build',
        preview: 'vite preview',
        lint: 'eslint . --ext ts,tsx',
        test: 'jest',
        'deploy:functions': 'supabase functions deploy',
        'sync:products': 'node scripts/sync-products.js',
        'sync:customers': 'node scripts/sync-customers.js'
      },
      dependencies: MODULE_CONFIG.dependencies.reduce((deps, dep) => {
        deps[dep] = 'latest';
        return deps;
      }, {}),
      peerDependencies: {
        react: '>=18.0.0',
        'react-dom': '>=18.0.0'
      },
      keywords: ['woocommerce', 'ecommerce', 'integration', 'sync', 'quantasori'],
      author: 'QuantaSori Development Team',
      license: 'MIT'
    };
    
    await fs.writeJson(path.join(DEST_DIR, 'package.json'), packageJson, { spaces: 2 });
    console.log('✅ Created WooCommerce module package.json');
    
    // Create README
    const readme = `# 🛒 WooCommerce Integration Module

${MODULE_CONFIG.description}

## Features

- **Real-time Product Sync** with WooCommerce stores
- **Customer Management** with comprehensive analytics
- **Order Processing** with automated workflows
- **Inventory Synchronization** across platforms
- **Customer Analytics** with detailed insights
- **Product Catalog Management** with bulk operations
- **Payment Integration** with multiple gateways
- **Shipping Management** with carrier integration
- **Webhook Handling** for real-time updates
- **Multi-store Support** for enterprise deployments

## Installation

\`\`\`bash
npm install @quantasori/woocommerce-integration
\`\`\`

## Configuration

\`\`\`env
WOOCOMMERCE_URL=https://your-store.com
WOOCOMMERCE_CONSUMER_KEY=your_consumer_key
WOOCOMMERCE_CONSUMER_SECRET=your_consumer_secret
WOOCOMMERCE_WEBHOOK_SECRET=your_webhook_secret
\`\`\`

## Usage

\`\`\`tsx
import { WooCommerceWrapper, CustomerManagement, ProductCatalog } from '@quantasori/woocommerce-integration';

function App() {
  return (
    <WooCommerceWrapper>
      <CustomerManagement />
      <ProductCatalog />
    </WooCommerceWrapper>
  );
}
\`\`\`

## Components

- \`WooCommerceWrapper\` - Main integration wrapper
- \`CustomerManagement\` - Customer CRUD and analytics
- \`ProductCatalog\` - Product management interface
- \`OrderProcessing\` - Order workflow management
- \`InventorySync\` - Real-time inventory updates

## API Endpoints

- \`/api/woocommerce/sync\` - Manual sync trigger
- \`/api/woocommerce/webhooks\` - Webhook handler
- \`/api/woocommerce/products\` - Product operations
- \`/api/woocommerce/customers\` - Customer operations
- \`/api/woocommerce/orders\` - Order operations

## License

MIT © QuantaSori Development Team
`;
    
    await fs.writeFile(path.join(DEST_DIR, 'README.md'), readme);
    console.log('✅ Created WooCommerce module README.md');
    
    return true;
  } catch (error) {
    console.error('❌ Error creating WooCommerce module structure:', error);
    return false;
  }
}

async function extractFiles() {
  console.log('🚀 Extracting WooCommerce module files...');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const [sourcePath, destPath] of Object.entries(EXTRACTION_MAP)) {
    try {
      const fullSourcePath = path.join(SOURCE_DIR, sourcePath);
      const fullDestPath = path.join(DEST_DIR, destPath);
      
      // Check if source exists
      if (await fs.pathExists(fullSourcePath)) {
        // Ensure destination directory exists
        await fs.ensureDir(path.dirname(fullDestPath));
        
        // Copy file or directory
        await fs.copy(fullSourcePath, fullDestPath, {
          overwrite: true,
          preserveTimestamps: true
        });
        
        console.log(`✅ Extracted: ${sourcePath} → ${destPath}`);
        successCount++;
      } else {
        console.log(`⚠️  Source not found: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Error extracting ${sourcePath}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n📊 WooCommerce Extraction Summary:`);
  console.log(`✅ Successfully extracted: ${successCount} items`);
  console.log(`❌ Errors: ${errorCount} items`);
  
  return errorCount === 0;
}

async function createModuleIndex() {
  console.log('📝 Creating WooCommerce module index file...');
  
  const indexContent = `/**
 * 🛒 WooCommerce Integration Module
 * 
 * Complete WooCommerce e-commerce integration with real-time sync capabilities
 * 
 * @version ${MODULE_CONFIG.version}
 * <AUTHOR> Development Team
 */

// Main Integration Component
export { default as WooCommerceWrapper } from './components/woocommerce/WooCommerceWrapper';

// Customer Management
export * from './components/customers';
export * from './components/customer-analytics';
export * from './components/customer-communication';
export * from './pages/customer-management';
export * from './pages/customer-hub';

// Product Management
export * from './pages/products';
export * from './types/product';

// Integration Pages
export { default as IntegrationsPage } from './pages/IntegrationsPage';

// Hooks
export * from './hooks/use-products';
export * from './hooks/useCustomerManagement';

// Services
export * from './services/customer-analytics';
export * from './services/customer-communication';

// Features
export * from './features/customers';

// Routes
export * from './routes/customerManagementRoutes';
export * from './routes/customerHubRoutes';
export * from './routes/productsRoutes';

// Utilities
export * from './lib/woocommerce-api';
export * from './lib/sync-helpers';
`;

  await fs.writeFile(path.join(DEST_DIR, 'src/index.ts'), indexContent);
  console.log('✅ Created WooCommerce module index file');
}

async function main() {
  console.log('🎬🎬🎬 WOOCOMMERCE MODULE EXTRACTION - LIGHTS, CAMERA, ACTION! 🎬🎬🎬');
  console.log('='.repeat(70));
  
  try {
    // Step 1: Create module structure
    const structureCreated = await createModuleStructure();
    if (!structureCreated) {
      throw new Error('Failed to create WooCommerce module structure');
    }
    
    // Step 2: Extract files
    const filesExtracted = await extractFiles();
    if (!filesExtracted) {
      console.log('⚠️  Some WooCommerce files failed to extract, but continuing...');
    }
    
    // Step 3: Create module index
    await createModuleIndex();
    
    console.log('\n🎉🎉🎉 WOOCOMMERCE MODULE EXTRACTION COMPLETE! 🎉🎉🎉');
    console.log(`📁 Module location: ${DEST_DIR}`);
    console.log('🚀 Ready for independent deployment and e-commerce integration!');
    
  } catch (error) {
    console.error('\n💥 WOOCOMMERCE EXTRACTION FAILED:', error.message);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default main;
