#!/usr/bin/env node

/**
 * 🚀 TECHHUB INTEGRATION ENHANCEMENTS IMPORT
 * 
 * This script imports NEW developments and enhancements from DOCUECHANGE modules
 * into the existing TechHub integration modules, adding new functionality
 * without replacing existing working components.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const CURRENT_DIR = __dirname;
const DOCUECHANGE_MODULES = path.resolve(__dirname, '../DOCUECHANGE/modules');

console.log('🎬🎬🎬 TECHHUB INTEGRATION ENHANCEMENTS IMPORT 🎬🎬🎬');
console.log('='.repeat(80));
console.log(`📁 Source: ${DOCUECHANGE_MODULES}`);
console.log(`📁 Destination: ${CURRENT_DIR}`);
console.log('='.repeat(80));

// Helper functions
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dirPath}`);
  }
}

function pathExists(filePath) {
  return fs.existsSync(filePath);
}

function copyFileWithEnhancement(src, dest, description) {
  try {
    // Create backup if file exists
    if (fs.existsSync(dest)) {
      const backupPath = `${dest}.backup-${Date.now()}`;
      fs.copyFileSync(dest, backupPath);
      console.log(`💾 Backed up existing: ${path.basename(dest)}`);
    }
    
    ensureDir(path.dirname(dest));
    fs.copyFileSync(src, dest);
    console.log(`✅ Enhanced: ${description}`);
    return true;
  } catch (error) {
    console.log(`❌ Error enhancing ${src}: ${error.message}`);
    return false;
  }
}

function copyDirRecursive(src, dest, description) {
  if (!fs.existsSync(src)) {
    console.log(`⚠️  Source not found: ${src}`);
    return 0;
  }

  ensureDir(dest);
  let count = 0;
  
  const items = fs.readdirSync(src);
  
  items.forEach(item => {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    
    if (fs.statSync(srcPath).isDirectory()) {
      count += copyDirRecursive(srcPath, destPath, `${description}/${item}`);
    } else {
      if (copyFileWithEnhancement(srcPath, destPath, `${description}/${item}`)) {
        count++;
      }
    }
  });
  
  return count;
}

// Import new WooCommerce enhancements
function importWooCommerceEnhancements() {
  console.log('\n🛒 IMPORTING WOOCOMMERCE ENHANCEMENTS...');
  console.log('-'.repeat(50));
  
  const wooModulePath = path.join(DOCUECHANGE_MODULES, 'woocommerce-integration/src');
  let totalFiles = 0;
  
  if (!pathExists(wooModulePath)) {
    console.log('⚠️  WooCommerce module not found in DOCUECHANGE');
    return 0;
  }
  
  // Import NEW customer analytics components
  const customerAnalyticsSrc = path.join(wooModulePath, 'components/customer-analytics');
  const customerAnalyticsDest = path.join(CURRENT_DIR, 'src/components/customer-analytics');
  if (pathExists(customerAnalyticsSrc)) {
    console.log('📊 Importing customer analytics enhancements...');
    totalFiles += copyDirRecursive(customerAnalyticsSrc, customerAnalyticsDest, 'Customer Analytics');
  }
  
  // Import NEW customer management components
  const customersSrc = path.join(wooModulePath, 'components/customers');
  const customersDest = path.join(CURRENT_DIR, 'src/components/customers');
  if (pathExists(customersSrc)) {
    console.log('👥 Importing customer management enhancements...');
    totalFiles += copyDirRecursive(customersSrc, customersDest, 'Customer Management');
  }
  
  // Import enhanced WooCommerce wrapper
  const wooWrapperSrc = path.join(wooModulePath, 'components/integrations/woocommerce/WooCommerceWrapper.tsx');
  const wooWrapperDest = path.join(CURRENT_DIR, 'src/components/integrations/WooCommerceWrapper.tsx');
  if (pathExists(wooWrapperSrc)) {
    if (copyFileWithEnhancement(wooWrapperSrc, wooWrapperDest, 'WooCommerce Integration Wrapper')) {
      totalFiles++;
    }
  }
  
  // Import WooCommerce pages
  const wooPagesSource = path.join(wooModulePath, 'pages');
  const wooPagesTarget = path.join(CURRENT_DIR, 'src/pages');
  if (pathExists(wooPagesSource)) {
    console.log('📄 Importing WooCommerce pages...');
    totalFiles += copyDirRecursive(wooPagesSource, wooPagesTarget, 'WooCommerce Pages');
  }
  
  // Import WooCommerce hooks
  const wooHooksSource = path.join(wooModulePath, 'hooks');
  const wooHooksTarget = path.join(CURRENT_DIR, 'src/hooks');
  if (pathExists(wooHooksSource)) {
    console.log('🎣 Importing WooCommerce hooks...');
    totalFiles += copyDirRecursive(wooHooksSource, wooHooksTarget, 'WooCommerce Hooks');
  }
  
  // Import WooCommerce routes
  const wooRoutesSource = path.join(wooModulePath, 'routes');
  const wooRoutesTarget = path.join(CURRENT_DIR, 'src/routes');
  if (pathExists(wooRoutesSource)) {
    console.log('🛣️  Importing WooCommerce routes...');
    totalFiles += copyDirRecursive(wooRoutesSource, wooRoutesTarget, 'WooCommerce Routes');
  }
  
  // Import WooCommerce types
  const wooTypesSource = path.join(wooModulePath, 'types');
  const wooTypesTarget = path.join(CURRENT_DIR, 'src/types');
  if (pathExists(wooTypesSource)) {
    console.log('📝 Importing WooCommerce types...');
    totalFiles += copyDirRecursive(wooTypesSource, wooTypesTarget, 'WooCommerce Types');
  }
  
  console.log(`📊 WooCommerce enhancements: ✅ ${totalFiles} files imported`);
  return totalFiles;
}

// Import new Odoo enhancements
function importOdooEnhancements() {
  console.log('\n🏢 IMPORTING ODOO ENHANCEMENTS...');
  console.log('-'.repeat(50));
  
  const odooModulePath = path.join(DOCUECHANGE_MODULES, 'odoo-integration/src');
  let totalFiles = 0;
  
  if (!pathExists(odooModulePath)) {
    console.log('⚠️  Odoo module not found in DOCUECHANGE');
    return 0;
  }
  
  // Import enhanced Odoo wrapper
  const odooWrapperSrc = path.join(odooModulePath, 'components/integrations/odoo/OdooIntegrationWrapper.tsx');
  const odooWrapperDest = path.join(CURRENT_DIR, 'src/components/integrations/OdooIntegrationWrapper.tsx');
  if (pathExists(odooWrapperSrc)) {
    if (copyFileWithEnhancement(odooWrapperSrc, odooWrapperDest, 'Odoo Integration Wrapper')) {
      totalFiles++;
    }
  }
  
  // Import enhanced financial components
  const financialSrc = path.join(odooModulePath, 'components/financial');
  const financialDest = path.join(CURRENT_DIR, 'src/components/financial');
  if (pathExists(financialSrc)) {
    console.log('💰 Importing financial system enhancements...');
    totalFiles += copyDirRecursive(financialSrc, financialDest, 'Financial System');
  }
  
  // Import enhanced project management components
  const pmSrc = path.join(odooModulePath, 'components/project-management');
  const pmDest = path.join(CURRENT_DIR, 'src/components/project-management');
  if (pathExists(pmSrc)) {
    console.log('📊 Importing project management enhancements...');
    totalFiles += copyDirRecursive(pmSrc, pmDest, 'Project Management');
  }
  
  // Import enhanced suppliers components
  const suppliersSrc = path.join(odooModulePath, 'components/suppliers');
  const suppliersDest = path.join(CURRENT_DIR, 'src/components/suppliers');
  if (pathExists(suppliersSrc)) {
    console.log('🏭 Importing supplier management enhancements...');
    totalFiles += copyDirRecursive(suppliersSrc, suppliersDest, 'Supplier Management');
  }
  
  // Import enhanced layouts
  const layoutsSrc = path.join(odooModulePath, 'layouts');
  const layoutsDest = path.join(CURRENT_DIR, 'src/layouts');
  if (pathExists(layoutsSrc)) {
    console.log('🎨 Importing layout enhancements...');
    totalFiles += copyDirRecursive(layoutsSrc, layoutsDest, 'Layouts');
  }
  
  // Import Odoo pages
  const odooPagesSource = path.join(odooModulePath, 'pages');
  const odooPagesTarget = path.join(CURRENT_DIR, 'src/pages');
  if (pathExists(odooPagesSource)) {
    console.log('📄 Importing Odoo pages...');
    totalFiles += copyDirRecursive(odooPagesSource, odooPagesTarget, 'Odoo Pages');
  }
  
  // Import Odoo routes
  const odooRoutesSource = path.join(odooModulePath, 'routes');
  const odooRoutesTarget = path.join(CURRENT_DIR, 'src/routes');
  if (pathExists(odooRoutesSource)) {
    console.log('🛣️  Importing Odoo routes...');
    totalFiles += copyDirRecursive(odooRoutesSource, odooRoutesTarget, 'Odoo Routes');
  }
  
  // Import Odoo services
  const servicesSource = path.join(odooModulePath, 'services');
  const servicesTarget = path.join(CURRENT_DIR, 'src/services');
  if (pathExists(servicesSource)) {
    console.log('⚙️  Importing Odoo services...');
    totalFiles += copyDirRecursive(servicesSource, servicesTarget, 'Odoo Services');
  }
  
  // Import Odoo types
  const odooTypesSource = path.join(odooModulePath, 'types');
  const odooTypesTarget = path.join(CURRENT_DIR, 'src/types');
  if (pathExists(odooTypesSource)) {
    console.log('📝 Importing Odoo types...');
    totalFiles += copyDirRecursive(odooTypesSource, odooTypesTarget, 'Odoo Types');
  }
  
  console.log(`📊 Odoo enhancements: ✅ ${totalFiles} files imported`);
  return totalFiles;
}

// Import admin enhancements
function importAdminEnhancements() {
  console.log('\n🔐 IMPORTING ADMIN ENHANCEMENTS...');
  console.log('-'.repeat(50));
  
  const adminModulePath = path.join(DOCUECHANGE_MODULES, 'admin-systems-config/src');
  let totalFiles = 0;
  
  if (!pathExists(adminModulePath)) {
    console.log('⚠️  Admin module not found in DOCUECHANGE');
    return 0;
  }
  
  // Import enhanced admin components
  const adminComponentsSrc = path.join(adminModulePath, 'components/admin');
  const adminComponentsDest = path.join(CURRENT_DIR, 'src/components/admin');
  if (pathExists(adminComponentsSrc)) {
    console.log('👨‍💼 Importing admin component enhancements...');
    totalFiles += copyDirRecursive(adminComponentsSrc, adminComponentsDest, 'Admin Components');
  }
  
  // Import enhanced auth components
  const authSrc = path.join(adminModulePath, 'auth');
  const authDest = path.join(CURRENT_DIR, 'src/auth');
  if (pathExists(authSrc)) {
    console.log('🔐 Importing auth enhancements...');
    totalFiles += copyDirRecursive(authSrc, authDest, 'Auth System');
  }
  
  // Import enhanced RBAC utilities
  const rbacSrc = path.join(adminModulePath, 'utils/rbac');
  const rbacDest = path.join(CURRENT_DIR, 'src/utils/rbac');
  if (pathExists(rbacSrc)) {
    console.log('🛡️  Importing RBAC enhancements...');
    totalFiles += copyDirRecursive(rbacSrc, rbacDest, 'RBAC System');
  }
  
  console.log(`📊 Admin enhancements: ✅ ${totalFiles} files imported`);
  return totalFiles;
}

// Main execution
async function main() {
  try {
    // Check if DOCUECHANGE modules directory exists
    if (!pathExists(DOCUECHANGE_MODULES)) {
      console.error(`❌ DOCUECHANGE modules directory not found: ${DOCUECHANGE_MODULES}`);
      process.exit(1);
    }
    
    // Import all enhancements
    const adminCount = importAdminEnhancements();
    const wooCount = importWooCommerceEnhancements();
    const odooCount = importOdooEnhancements();
    
    const totalFiles = adminCount + wooCount + odooCount;
    
    console.log('\n🎉🎉🎉 TECHHUB ENHANCEMENTS IMPORT COMPLETE! 🎉🎉🎉');
    console.log('='.repeat(80));
    console.log(`📊 Total enhancement files imported: ${totalFiles}`);
    console.log(`🔐 Admin enhancements: ${adminCount} files`);
    console.log(`🛒 WooCommerce enhancements: ${wooCount} files`);
    console.log(`🏢 Odoo enhancements: ${odooCount} files`);
    console.log('='.repeat(80));
    console.log('\n📝 Next steps:');
    console.log('1. Review imported enhancements for conflicts');
    console.log('2. Update TechHub navigation to include new features');
    console.log('3. Test enhanced integration functionality');
    console.log('4. Update route configuration if needed');
    console.log('5. Run npm install if new dependencies were added');
    
  } catch (error) {
    console.error('💥 ENHANCEMENT IMPORT FAILED:', error.message);
    process.exit(1);
  }
}

// Run the import
main();
