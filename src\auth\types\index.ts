// Import Permission and UserRole from the main RBAC module
export { Permission, UserRole } from '@/auth/utils/rbac/permissions';

export interface UserProfile {
  id: string;
  user_id: string;
  email: string;
  username: string;
  role: UserRole;
  permissions: string[];
  first_name?: string;
  last_name?: string;
  display_name?: string;
  phone?: string;
  avatar_url?: string;
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface AuthContextType {
  user: UserProfile | null;
  session: any;
  loading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  hasPermission: (permission: Permission | string) => boolean;
  hasAnyPermission: (permissions: (Permission | string)[]) => boolean;
  hasAllPermissions: (permissions: (Permission | string)[]) => boolean;
  isRole: (role: UserRole) => boolean;
  isRoleOrHigher: (role: UserRole) => boolean;
}
