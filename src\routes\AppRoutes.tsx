import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { PlatformLayout } from '@/components/layouts/PlatformLayout';
import { NavCategory, NavItem } from '@/components/layout/sidebar/types';

// Pages
import Index from '@/pages/Index';
import NotFound from '@/pages/NotFound';
import Settings from '@/pages/Settings';
import Landing from '@/pages/Landing';
import Profile from '@/pages/Profile';
import AccountSecurity from '@/pages/AccountSecurity';
import AccountBilling from '@/pages/AccountBilling';
import Activity from '@/pages/Activity';
import Help from '@/pages/Help';
import RootHandler from '@/components/RootHandler';
import MasterDash from '@/pages/MasterDash';
import Unauthorized from '@/pages/Unauthorized';
import SharedDocumentPage from '@/pages/SharedDocumentPage';
import Dashboard from '@/pages/Dashboard';
import DashboardV2 from '@/pages/DashboardV2';

// Import module route configurations
import { AdminRoutes } from "./adminRoutes";
import { DataManagementRoutes } from "./dataManagementRoutes";
import { LoyaltyRoutes } from "./loyaltyRoutes";
import { TradingSystemRoutes } from "./tradingSystemRoutes";
import TechHubRoutes from "./techHubRoutesNew"; // Corrected import to default
import { DotXRoutes } from "./dotXRoutes";
import { CustomerManagementRoutes } from "./customerManagementRoutes";
import { BrandMarketingRoutes } from "./brandMarketingRoutes";
import { BrandManagementRoutes } from "./brandManagementRoutes";
import { SocialMediaRoutes } from "./socialMediaRoutes";
import { MarketingRoutes } from "./marketingRoutes";
import ProjectManagementRoutes from "./projectManagementRoutes";
import { FinancialRoutes } from "./financialRoutes"; // Import FinancialRoutes
import { RAGDashboardRoutes } from "./ragDashboardRoutes";
import { EventProductionRoutes } from "./eventProductionRoutes";
import { RentalsEquipmentRoutes } from "./rentalsEquipmentRoutes";
import { AIExtractRoutes } from "./aiExtractRoutes";
import { RepoRoutes } from "./repoRoutes";
import { SupplyChainRoutes } from "./supplyChainRoutes";

// Import layout configuration
import { navCategories } from '@/components/layout/sidebar/NavigationConfig';
import { Home, Settings as SettingsIcon } from 'lucide-react';

export const AppRoutes = () => {
  // Define main navigation categories for standard page layout
  const mainNavCategories: NavCategory[] = [
    {
      name: "Main",
      label: "Main",
      items: [
        { label: "Dashboard", path: "/master", icon: Home },
        { label: "Settings", path: "/settings", icon: SettingsIcon }
      ]
    }
  ];

  return (
    <Router>
      <Routes>
        {/* Root/Index Route */}
        <Route index element={<RootHandler />} />
        <Route path="/" element={<Navigate to="/master" replace />} />
        
        {/* Settings page with PlatformLayout */}
        <Route 
          path="/settings" 
          element={
            <PlatformLayout 
              moduleTitle="Settings"
            >
              <Settings />
            </PlatformLayout>
          } 
        />
        
        {/* User Account Pages */}
        <Route
          path="/profile"
          element={
            <PlatformLayout
              moduleTitle="Profile"
            >
              <Profile />
            </PlatformLayout>
          }
        />
        
        <Route
          path="/account/security"
          element={
            <PlatformLayout
              moduleTitle="Account Security"
            >
              <AccountSecurity />
            </PlatformLayout>
          }
        />
        
        <Route
          path="/account/billing"
          element={
            <PlatformLayout
              moduleTitle="Billing & Subscription"
            >
              <AccountBilling />
            </PlatformLayout>
          }
        />
        
        <Route
          path="/activity"
          element={
            <PlatformLayout
              moduleTitle="Activity Log"
            >
              <Activity />
            </PlatformLayout>
          }
        />
        
        <Route
          path="/help"
          element={
            <PlatformLayout
              moduleTitle="Help & Support"
            >
              <Help />
            </PlatformLayout>
          }
        />
        
        {/* Authentication Pages */}
        <Route path="/landing" element={<Landing />} />
        <Route path="/unauthorized" element={<Unauthorized />} />
        
        {/* Dashboard Pages */}
        <Route
          path="/dashboard"
          element={
            <PlatformLayout
              moduleTitle="Dashboard"
            >
              <Dashboard />
            </PlatformLayout>
          }
        />
        <Route path="/dashboard-v2" element={<DashboardV2 />} />
        <Route path="/master" element={<MasterDash />} />
        <Route path="/master-modules" element={<MasterDash />} />
        
        {/* Shared Document Route */}
        <Route path="/shared-document/:shareId" element={<SharedDocumentPage />} />
        
        {/* Module-specific Routes - imported from separate route files */}
        <Route path="/admin/*" element={<AdminRoutes />} />
        <Route path="/data-management/*" element={<DataManagementRoutes />} />
        <Route path="/loyalty/*" element={<LoyaltyRoutes />} />
        <Route path="/trading-system/*" element={<TradingSystemRoutes />} />
        <Route path="/tech-hub/*" element={<TechHubRoutes />} />
        <Route path="/dot-x/*" element={<DotXRoutes />} />
        <Route path="/customer-management/*" element={<CustomerManagementRoutes />} />
        <Route path="/brand-marketing/*" element={<BrandMarketingRoutes />} />
        <Route path="/brand-management/*" element={<BrandManagementRoutes />} />
        <Route path="/social-media/*" element={<SocialMediaRoutes />} />
        <Route path="/marketing/*" element={<MarketingRoutes />} />
        <Route path="/events/*" element={<EventsRoutes />} />
        <Route path="/rentals-equipment/*" element={<RentalsEquipmentRoutes />} />
        <Route path="/project-management/*" element={<ProjectManagementRoutes />} />
        <Route path="/rag-dashboard/*" element={<RAGDashboardRoutes />} />
        <Route path="/contracts/*" element={<ContractsRoutes />} />
        <Route path="/financial/*" element={<FinancialRoutes />} />
        <Route path="/categories/*" element={<CategoriesRoutes />} />
        <Route path="/entities/*" element={<EntitiesRoutes />} />
        <Route path="/scorecards/*" element={<ScorecardsRoutes />} />
        <Route path="/workflows/*" element={<WorkflowsRoutes />} />
        <Route path="/ai-extract/*" element={<AIExtractRoutes />} />
        <Route path="/files/*" element={<FilesRoutes />} />
        <Route path="/events/*" element={<EventsRoutes />} />
        <Route path="/risk-register/*" element={<RiskRegisterRoutes />} />
        <Route path="/requests/*" element={<RequestsRoutes />} />
        <Route path="/repo/*" element={<RepoRoutes />} />
        <Route path="/supply-chain/*" element={<SupplyChainRoutes />} />

        {/* Add legacy beta redirects */}
        <Route path="/beta1/*" element={<Navigate to="/data-management" replace />} />
        <Route path="/beta2/*" element={<Navigate to="/loyalty-rewards" replace />} />
        
        {/* Supply Chain Integration Redirects */}
        <Route path="/supplier-management/*" element={<Navigate to="/supply-chain/supplier-management" replace />} />
        <Route path="/vendor-management/*" element={<Navigate to="/supply-chain/vendor-management" replace />} />
        <Route path="/inventory-stock/*" element={<Navigate to="/supply-chain/inventory-stock" replace />} />
        <Route path="/costing/*" element={<Navigate to="/supply-chain/costing" replace />} />

        {/* Legacy route redirects */}
        <Route path="/prototypes" element={<Navigate to="/" replace />} />
        <Route path="/supplier-settings" element={<Navigate to="/supply-chain/supplier-management" replace />} />
        <Route path="/supplier-costing" element={<Navigate to="/supply-chain/costing" replace />} />
        <Route path="/cost-analysis" element={<Navigate to="/supply-chain/costing" replace />} />
        
        {/* 404 Page */}
        <Route path="*" element={
          <PlatformLayout
            moduleTitle="Not Found"
          >
            <NotFound />
          </PlatformLayout>
        } />
      </Routes>
    </Router>
  );
};

export default AppRoutes;
