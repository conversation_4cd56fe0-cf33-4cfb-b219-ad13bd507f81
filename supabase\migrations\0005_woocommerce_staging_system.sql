-- WooCommerce Staging System for Real-Time Data Synchronization
-- This migration creates staging tables and sync infrastructure

-- WooCommerce Configuration Table
CREATE TABLE public.woocommerce_config (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES public.organizations(id),
    store_url text NOT NULL,
    consumer_key text NOT NULL,
    consumer_secret text NOT NULL,
    webhook_secret text,
    api_version varchar(10) DEFAULT 'v3',
    sync_enabled boolean DEFAULT true,
    last_sync_at timestamp with time zone,
    sync_status varchar(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'active', 'error', 'disabled')),
    batch_size integer DEFAULT 100 CHECK (batch_size BETWEEN 10 AND 1000),
    sync_frequency_minutes integer DEFAULT 15 CHECK (sync_frequency_minutes >= 5),
    error_count integer DEFAULT 0,
    last_error_message text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- WooCommerce Customers Staging Table
CREATE TABLE public.woocommerce_customers_staging (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    wc_customer_id integer NOT NULL,
    organization_id uuid NOT NULL REFERENCES public.organizations(id),
    email varchar(255) NOT NULL,
    first_name varchar(100),
    last_name varchar(100),
    username varchar(100),
    date_created timestamp with time zone,
    date_modified timestamp with time zone,
    billing_first_name varchar(100),
    billing_last_name varchar(100),
    billing_company varchar(200),
    billing_address_1 text,
    billing_address_2 text,
    billing_city varchar(100),
    billing_state varchar(100),
    billing_postcode varchar(20),
    billing_country varchar(5),
    billing_email varchar(255),
    billing_phone varchar(50),
    shipping_first_name varchar(100),
    shipping_last_name varchar(100),
    shipping_company varchar(200),
    shipping_address_1 text,
    shipping_address_2 text,
    shipping_city varchar(100),
    shipping_state varchar(100),
    shipping_postcode varchar(20),
    shipping_country varchar(5),
    is_paying_customer boolean DEFAULT false,
    avatar_url text,
    meta_data jsonb DEFAULT '[]',
    raw_data jsonb NOT NULL,
    sync_status varchar(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'error', 'skipped')),
    sync_error text,
    synced_to_customer_id uuid REFERENCES public.customers(id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(organization_id, wc_customer_id)
);

-- WooCommerce Products Staging Table
CREATE TABLE public.woocommerce_products_staging (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    wc_product_id integer NOT NULL,
    organization_id uuid NOT NULL REFERENCES public.organizations(id),
    name varchar(255) NOT NULL,
    slug varchar(255),
    permalink text,
    date_created timestamp with time zone,
    date_modified timestamp with time zone,
    type varchar(50) DEFAULT 'simple',
    status varchar(20) DEFAULT 'publish',
    featured boolean DEFAULT false,
    catalog_visibility varchar(20) DEFAULT 'visible',
    description text,
    short_description text,
    sku varchar(100),
    price numeric(10,2),
    regular_price numeric(10,2),
    sale_price numeric(10,2),
    date_on_sale_from timestamp with time zone,
    date_on_sale_to timestamp with time zone,
    price_html text,
    on_sale boolean DEFAULT false,
    purchasable boolean DEFAULT true,
    total_sales integer DEFAULT 0,
    virtual boolean DEFAULT false,
    downloadable boolean DEFAULT false,
    downloads jsonb DEFAULT '[]',
    download_limit integer DEFAULT -1,
    download_expiry integer DEFAULT -1,
    external_url text,
    button_text varchar(100),
    tax_status varchar(20) DEFAULT 'taxable',
    tax_class varchar(50),
    manage_stock boolean DEFAULT false,
    stock_quantity integer,
    stock_status varchar(20) DEFAULT 'instock',
    backorders varchar(20) DEFAULT 'no',
    backorders_allowed boolean DEFAULT false,
    backordered boolean DEFAULT false,
    sold_individually boolean DEFAULT false,
    weight varchar(20),
    dimensions jsonb DEFAULT '{}',
    shipping_required boolean DEFAULT true,
    shipping_taxable boolean DEFAULT true,
    shipping_class varchar(100),
    shipping_class_id integer,
    reviews_allowed boolean DEFAULT true,
    average_rating varchar(10) DEFAULT '0',
    rating_count integer DEFAULT 0,
    related_ids jsonb DEFAULT '[]',
    upsell_ids jsonb DEFAULT '[]',
    cross_sell_ids jsonb DEFAULT '[]',
    parent_id integer DEFAULT 0,
    purchase_note text,
    categories jsonb DEFAULT '[]',
    tags jsonb DEFAULT '[]',
    images jsonb DEFAULT '[]',
    attributes jsonb DEFAULT '[]',
    default_attributes jsonb DEFAULT '[]',
    variations jsonb DEFAULT '[]',
    grouped_products jsonb DEFAULT '[]',
    menu_order integer DEFAULT 0,
    meta_data jsonb DEFAULT '[]',
    raw_data jsonb NOT NULL,
    sync_status varchar(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'error', 'skipped')),
    sync_error text,
    synced_to_equipment_id uuid REFERENCES public.equipment_catalog(id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(organization_id, wc_product_id)
);

-- WooCommerce Orders Staging Table
CREATE TABLE public.woocommerce_orders_staging (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    wc_order_id integer NOT NULL,
    organization_id uuid NOT NULL REFERENCES public.organizations(id),
    parent_id integer DEFAULT 0,
    number varchar(50),
    order_key varchar(100),
    created_via varchar(50),
    version varchar(20),
    status varchar(50),
    currency varchar(5) DEFAULT 'ZAR',
    date_created timestamp with time zone,
    date_modified timestamp with time zone,
    discount_total numeric(10,2) DEFAULT 0,
    discount_tax numeric(10,2) DEFAULT 0,
    shipping_total numeric(10,2) DEFAULT 0,
    shipping_tax numeric(10,2) DEFAULT 0,
    cart_tax numeric(10,2) DEFAULT 0,
    total numeric(10,2) NOT NULL,
    total_tax numeric(10,2) DEFAULT 0,
    prices_include_tax boolean DEFAULT false,
    customer_id integer,
    customer_ip_address varchar(45),
    customer_user_agent text,
    customer_note text,
    billing jsonb DEFAULT '{}',
    shipping jsonb DEFAULT '{}',
    payment_method varchar(100),
    payment_method_title varchar(200),
    transaction_id varchar(200),
    date_paid timestamp with time zone,
    date_completed timestamp with time zone,
    cart_hash varchar(100),
    meta_data jsonb DEFAULT '[]',
    line_items jsonb DEFAULT '[]',
    tax_lines jsonb DEFAULT '[]',
    shipping_lines jsonb DEFAULT '[]',
    fee_lines jsonb DEFAULT '[]',
    coupon_lines jsonb DEFAULT '[]',
    refunds jsonb DEFAULT '[]',
    raw_data jsonb NOT NULL,
    sync_status varchar(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'error', 'skipped')),
    sync_error text,
    synced_to_rental_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(organization_id, wc_order_id)
);

-- Sync Log Table
CREATE TABLE public.woocommerce_sync_log (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES public.organizations(id),
    sync_type varchar(50) NOT NULL CHECK (sync_type IN ('customers', 'products', 'orders', 'full_sync')),
    sync_mode varchar(20) NOT NULL CHECK (sync_mode IN ('batch', 'incremental', 'full')),
    batch_size integer,
    records_processed integer DEFAULT 0,
    records_success integer DEFAULT 0,
    records_error integer DEFAULT 0,
    records_skipped integer DEFAULT 0,
    status varchar(20) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
    started_at timestamp with time zone DEFAULT now(),
    completed_at timestamp with time zone,
    error_message text,
    sync_details jsonb DEFAULT '{}',
    created_by uuid REFERENCES auth.users(id)
);

-- Create indexes for performance
CREATE INDEX idx_wc_customers_staging_org_id ON public.woocommerce_customers_staging(organization_id);
CREATE INDEX idx_wc_customers_staging_wc_id ON public.woocommerce_customers_staging(wc_customer_id);
CREATE INDEX idx_wc_customers_staging_email ON public.woocommerce_customers_staging(email);
CREATE INDEX idx_wc_customers_staging_sync_status ON public.woocommerce_customers_staging(sync_status);

CREATE INDEX idx_wc_products_staging_org_id ON public.woocommerce_products_staging(organization_id);
CREATE INDEX idx_wc_products_staging_wc_id ON public.woocommerce_products_staging(wc_product_id);
CREATE INDEX idx_wc_products_staging_sku ON public.woocommerce_products_staging(sku);
CREATE INDEX idx_wc_products_staging_sync_status ON public.woocommerce_products_staging(sync_status);

CREATE INDEX idx_wc_orders_staging_org_id ON public.woocommerce_orders_staging(organization_id);
CREATE INDEX idx_wc_orders_staging_wc_id ON public.woocommerce_orders_staging(wc_order_id);
CREATE INDEX idx_wc_orders_staging_customer_id ON public.woocommerce_orders_staging(customer_id);
CREATE INDEX idx_wc_orders_staging_sync_status ON public.woocommerce_orders_staging(sync_status);

CREATE INDEX idx_wc_sync_log_org_id ON public.woocommerce_sync_log(organization_id);
CREATE INDEX idx_wc_sync_log_type_status ON public.woocommerce_sync_log(sync_type, status);

-- Enable RLS
ALTER TABLE public.woocommerce_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.woocommerce_customers_staging ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.woocommerce_products_staging ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.woocommerce_orders_staging ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.woocommerce_sync_log ENABLE ROW LEVEL SECURITY;
