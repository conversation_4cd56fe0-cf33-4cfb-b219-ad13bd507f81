-- ================================================================================================
-- Unified Database Schema - Foundational Migration
-- Version: 1.1
-- Description: This script completely resets the public schema and rebuilds it from scratch.
--              It is DESTRUCTIVE and will remove all data.
-- ================================================================================================

-- Section 0: Schema Reset
DROP SCHEMA IF EXISTS public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;

-- ================================================================================================
-- Section 1: Custom Types
-- ================================================================================================
CREATE TYPE public.course_type AS ENUM ('equipment', 'engineering', 'safety', 'business');
CREATE TYPE public.course_category AS ENUM ('audio', 'lighting', 'video', 'rigging', 'power', 'management');
CREATE TYPE public.difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');
CREATE TYPE public.access_model AS ENUM ('free', 'one_time_purchase', 'subscription');
CREATE TYPE public.lesson_type AS ENUM ('video', 'reading', 'quiz', 'interactive_lab', 'assignment');
CREATE TYPE public.content_type AS ENUM ('video', 'document', 'image', 'assessment', 'external_link');
CREATE TYPE public.enrollment_type AS ENUM ('individual', 'group', 'subscription_based');

-- ================================================================================================
-- Section 2: Tables (Creation in dependency order)
-- ================================================================================================

-- Core Infrastructure
CREATE TABLE public.organizations (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL CHECK (length(TRIM(BOTH FROM name)) > 0),
  legal_name character varying NOT NULL CHECK (length(TRIM(BOTH FROM legal_name)) > 0),
  tax_id character varying,
  registration_number character varying,
  industry character varying,
  employee_count integer DEFAULT 0 CHECK (employee_count >= 0),
  headquarters_address text,
  phone character varying,
  email character varying CHECK (email::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text OR email IS NULL),
  website character varying,
  settings jsonb DEFAULT '{}'::jsonb,
  compliance_config jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT organizations_pkey PRIMARY KEY (id)
);

CREATE TABLE public.departments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  parent_department_id uuid REFERENCES public.departments(id),
  name character varying NOT NULL CHECK (length(TRIM(BOTH FROM name)) > 0),
  code character varying NOT NULL CHECK (length(TRIM(BOTH FROM code)) > 0),
  description text,
  manager_id uuid, -- FK added later
  cost_center_code character varying,
  budget_allocation jsonb DEFAULT '{}'::jsonb,
  location character varying,
  email character varying CHECK (email::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text OR email IS NULL),
  phone character varying,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT departments_pkey PRIMARY KEY (id)
);

CREATE TABLE public.positions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  department_id uuid REFERENCES public.departments(id),
  title character varying NOT NULL CHECK (length(TRIM(BOTH FROM title)) > 0),
  code character varying NOT NULL CHECK (length(TRIM(BOTH FROM code)) > 0),
  description text,
  requirements text,
  responsibilities text,
  salary_range jsonb DEFAULT '{}'::jsonb,
  employment_type character varying NOT NULL DEFAULT 'PERMANENT'::character varying CHECK (employment_type::text = ANY (ARRAY['PERMANENT'::character varying, 'FIXED_TERM'::character varying, 'CONTRACTOR'::character varying, 'INTERN'::character varying, 'PART_TIME'::character varying, 'TEMPORARY'::character varying, 'CONSULTANT'::character varying]::text[])),
  level integer DEFAULT 1 CHECK (level > 0),
  reports_to_position_id uuid REFERENCES public.positions(id),
  location character varying,
  is_remote_eligible boolean DEFAULT false,
  required_skills text[],
  preferred_skills text[],
  education_requirements text,
  experience_years_min integer DEFAULT 0,
  experience_years_max integer,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT positions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.employees (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  department_id uuid REFERENCES public.departments(id),
  position_id uuid REFERENCES public.positions(id),
  manager_id uuid REFERENCES public.employees(id),
  user_id uuid REFERENCES auth.users(id),
  employee_number character varying NOT NULL CHECK (length(TRIM(BOTH FROM employee_number)) > 0),
  first_name character varying NOT NULL CHECK (length(TRIM(BOTH FROM first_name)) > 0),
  last_name character varying NOT NULL CHECK (length(TRIM(BOTH FROM last_name)) > 0),
  middle_name character varying,
  preferred_name character varying,
  email character varying NOT NULL CHECK (email::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text),
  personal_email character varying CHECK (personal_email::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text OR personal_email IS NULL),
  phone character varying,
  mobile_phone character varying,
  date_of_birth date,
  gender character varying CHECK ((gender::text = ANY (ARRAY['Male'::character varying, 'Female'::character varying, 'Non-binary'::character varying, 'Prefer not to say'::character varying, 'Other'::character varying]::text[])) OR gender IS NULL),
  nationality character varying,
  marital_status character varying CHECK ((marital_status::text = ANY (ARRAY['Single'::character varying, 'Married'::character varying, 'Divorced'::character varying, 'Widowed'::character varying, 'Separated'::character varying, 'Domestic Partnership'::character varying, 'Prefer not to say'::character varying]::text[])) OR marital_status IS NULL),
  address text,
  postal_code character varying,
  city character varying,
  state_province character varying,
  country character varying DEFAULT 'South Africa'::character varying,
  emergency_contact_name character varying,
  emergency_contact_relationship character varying,
  emergency_contact_phone character varying,
  emergency_contact_email character varying CHECK (emergency_contact_email::text ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text OR emergency_contact_email IS NULL),
  hire_date date NOT NULL CHECK (hire_date >= '1900-01-01'::date AND hire_date <= (CURRENT_DATE + '1 year'::interval)),
  probation_end_date date,
  termination_date date,
  termination_reason text,
  employment_status character varying NOT NULL DEFAULT 'ACTIVE'::character varying CHECK (employment_status::text = ANY (ARRAY['ACTIVE'::character varying, 'INACTIVE'::character varying, 'TERMINATED'::character varying, 'ON_LEAVE'::character varying, 'SUSPENDED'::character varying, 'PROBATION'::character varying, 'NOTICE_PERIOD'::character varying]::text[])),
  work_location character varying,
  is_remote boolean DEFAULT false,
  personal_details jsonb DEFAULT '{}'::jsonb,
  system_access jsonb DEFAULT '{}'::jsonb,
  benefits_info jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT employees_pkey PRIMARY KEY (id)
);

-- Add deferred FK for circular dependency
ALTER TABLE public.departments ADD CONSTRAINT fk_departments_manager FOREIGN KEY (manager_id) REFERENCES public.employees(id) DEFERRABLE INITIALLY DEFERRED;

-- CRM & Financials
CREATE TABLE public.customers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  customer_type character varying NOT NULL DEFAULT 'Individual'::character varying CHECK (customer_type::text = ANY (ARRAY['Individual'::character varying, 'Corporate'::character varying, 'Event_Company'::character varying, 'Educational'::character varying, 'Government'::character varying, 'Non_Profit'::character varying]::text[])),
  company_name character varying,
  contact_name character varying NOT NULL,
  email character varying NOT NULL,
  phone character varying,
  address text,
  city character varying,
  state_province character varying,
  postal_code character varying,
  country character varying DEFAULT 'South Africa'::character varying,
  tax_number character varying,
  registration_number character varying,
  credit_status character varying NOT NULL DEFAULT 'Good'::character varying CHECK (credit_status::text = ANY (ARRAY['Excellent'::character varying, 'Good'::character varying, 'Fair'::character varying, 'Poor'::character varying, 'Blocked'::character varying]::text[])),
  credit_limit numeric DEFAULT 0 CHECK (credit_limit >= 0::numeric),
  current_balance numeric DEFAULT 0 CHECK (current_balance >= 0::numeric),
  preferences jsonb DEFAULT '{}'::jsonb,
  event_history jsonb DEFAULT '[]'::jsonb,
  communication_preferences jsonb DEFAULT '{}'::jsonb,
  emergency_contact_name character varying,
  emergency_contact_phone character varying,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT customers_pkey PRIMARY KEY (id)
);

CREATE TABLE public.vendors (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  vendor_code character varying NOT NULL UNIQUE CHECK (vendor_code::text ~ '^[A-Z0-9]{3,10}$'::text),
  company_name character varying NOT NULL,
  legal_name character varying,
  vendor_type character varying DEFAULT 'supplier'::character varying CHECK (vendor_type::text = ANY (ARRAY['supplier'::character varying, 'manufacturer'::character varying, 'service_provider'::character varying, 'contractor'::character varying]::text[])),
  tax_id character varying,
  registration_number character varying,
  primary_contact_name character varying,
  primary_contact_email character varying,
  primary_contact_phone character varying,
  billing_address_line1 character varying,
  billing_address_line2 character varying,
  billing_city character varying,
  billing_state character varying,
  billing_postal_code character varying,
  billing_country character varying DEFAULT 'United Kingdom'::character varying,
  payment_terms integer DEFAULT 30,
  credit_limit numeric DEFAULT 0,
  current_balance numeric DEFAULT 0,
  currency character varying DEFAULT 'GBP'::character varying,
  status character varying DEFAULT 'active'::character varying CHECK (status::text = ANY (ARRAY['active'::character varying, 'inactive'::character varying, 'suspended'::character varying, 'pending'::character varying]::text[])),
  credit_status character varying DEFAULT 'good'::character varying CHECK (credit_status::text = ANY (ARRAY['good'::character varying, 'warning'::character varying, 'restricted'::character varying, 'blocked'::character varying]::text[])),
  quality_rating integer CHECK (quality_rating >= 1 AND quality_rating <= 5),
  compliance_status character varying DEFAULT 'compliant'::character varying CHECK (compliance_status::text = ANY (ARRAY['compliant'::character varying, 'non_compliant'::character varying, 'under_review'::character varying]::text[])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid REFERENCES auth.users(id),
  updated_by uuid REFERENCES auth.users(id),
  website character varying,
  notes text,
  tags text[],
  CONSTRAINT vendors_pkey PRIMARY KEY (id)
);

CREATE TABLE public.chart_of_accounts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  account_code character varying NOT NULL,
  account_name character varying NOT NULL,
  account_type character varying NOT NULL CHECK (account_type::text = ANY (ARRAY['Asset'::character varying, 'Liability'::character varying, 'Equity'::character varying, 'Revenue'::character varying, 'Expense'::character varying]::text[])),
  account_subtype character varying,
  parent_account_id uuid REFERENCES public.chart_of_accounts(id),
  is_active boolean DEFAULT true,
  description text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT chart_of_accounts_pkey PRIMARY KEY (id)
);

CREATE TABLE public.general_ledger (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  transaction_id uuid NOT NULL,
  account_id uuid NOT NULL REFERENCES public.chart_of_accounts(id),
  transaction_date date NOT NULL,
  description text NOT NULL,
  debit_amount numeric DEFAULT 0 CHECK (debit_amount >= 0::numeric),
  credit_amount numeric DEFAULT 0 CHECK (credit_amount >= 0::numeric),
  reference_type character varying,
  reference_id uuid,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT general_ledger_pkey PRIMARY KEY (id)
);

CREATE TABLE public.journal_entries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  entry_number character varying NOT NULL,
  entry_date date NOT NULL,
  description text NOT NULL,
  total_debit numeric NOT NULL,
  total_credit numeric NOT NULL,
  status character varying NOT NULL DEFAULT 'Draft'::character varying CHECK (status::text = ANY (ARRAY['Draft'::character varying, 'Posted'::character varying, 'Reversed'::character varying]::text[])),
  reference_type character varying,
  reference_id uuid,
  created_by uuid NOT NULL,
  posted_by uuid,
  posted_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT journal_entries_pkey PRIMARY KEY (id)
);

CREATE TABLE public.journal_entry_lines (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  journal_entry_id uuid NOT NULL REFERENCES public.journal_entries(id),
  account_id uuid NOT NULL REFERENCES public.chart_of_accounts(id),
  description text,
  debit_amount numeric DEFAULT 0 CHECK (debit_amount >= 0::numeric),
  credit_amount numeric DEFAULT 0 CHECK (credit_amount >= 0::numeric),
  line_number integer NOT NULL,
  CONSTRAINT journal_entry_lines_pkey PRIMARY KEY (id)
);

CREATE TABLE public.accounts_payable (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  vendor_id uuid NOT NULL,
  invoice_number character varying NOT NULL,
  invoice_date date NOT NULL,
  due_date date NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  amount_paid numeric DEFAULT 0 CHECK (amount_paid >= 0::numeric),
  amount_due numeric GENERATED ALWAYS AS (amount - amount_paid) STORED,
  status character varying NOT NULL DEFAULT 'Open'::character varying CHECK (status::text = ANY (ARRAY['Open'::character varying, 'Paid'::character varying, 'Overdue'::character varying, 'Cancelled'::character varying]::text[])),
  description text,
  payment_terms character varying,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT accounts_payable_pkey PRIMARY KEY (id)
);

CREATE TABLE public.accounts_receivable (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES public.organizations(id),
    customer_id uuid NOT NULL REFERENCES public.customers(id),
    invoice_number character varying NOT NULL,
    invoice_date date NOT NULL,
    due_date date NOT NULL,
    amount numeric NOT NULL CHECK (amount > 0),
    amount_paid numeric DEFAULT 0 CHECK (amount_paid >= 0),
    amount_due numeric GENERATED ALWAYS AS (amount - amount_paid) STORED,
    status character varying NOT NULL DEFAULT 'Open' CHECK (status IN ('Open', 'Paid', 'Overdue', 'Cancelled')),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT accounts_receivable_pkey PRIMARY KEY (id)
);

CREATE TABLE public.payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  payment_number character varying NOT NULL,
  payment_date date NOT NULL,
  payment_type character varying NOT NULL CHECK (payment_type::text = ANY (ARRAY['Received'::character varying, 'Made'::character varying]::text[])),
  payment_method character varying NOT NULL,
  amount numeric NOT NULL CHECK (amount > 0::numeric),
  reference_number character varying,
  description text,
  bank_account_id uuid,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT payments_pkey PRIMARY KEY (id)
);

CREATE TABLE public.bank_accounts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  account_name character varying NOT NULL,
  account_number character varying NOT NULL,
  bank_name character varying NOT NULL,
  account_type character varying NOT NULL,
  current_balance numeric DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT bank_accounts_pkey PRIMARY KEY (id)
);

CREATE TABLE public.tax_codes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  tax_code character varying NOT NULL,
  tax_name character varying NOT NULL,
  tax_rate numeric NOT NULL CHECK (tax_rate >= 0::numeric),
  tax_type character varying NOT NULL,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT tax_codes_pkey PRIMARY KEY (id)
);

-- Supply Chain & Inventory
CREATE TABLE public.purchase_orders (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  po_number character varying NOT NULL UNIQUE CHECK (po_number::text ~ '^PO[0-9]{6,10}$'::text),
  vendor_id uuid NOT NULL REFERENCES public.vendors(id),
  order_date date NOT NULL DEFAULT CURRENT_DATE,
  required_date date,
  promised_date date,
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'pending_approval'::character varying, 'approved'::character varying, 'sent'::character varying, 'acknowledged'::character varying, 'partially_received'::character varying, 'fully_received'::character varying, 'closed'::character varying, 'cancelled'::character varying]::text[])),
  subtotal numeric NOT NULL DEFAULT 0,
  tax_amount numeric NOT NULL DEFAULT 0,
  total_amount numeric NOT NULL DEFAULT 0,
  currency character varying DEFAULT 'GBP'::character varying,
  delivery_address_line1 character varying,
  delivery_address_line2 character varying,
  delivery_city character varying,
  delivery_state character varying,
  delivery_postal_code character varying,
  delivery_country character varying DEFAULT 'United Kingdom'::character varying,
  approval_status character varying DEFAULT 'pending'::character varying CHECK (approval_status::text = ANY (ARRAY['pending'::character varying, 'approved'::character varying, 'rejected'::character varying, 'requires_revision'::character varying]::text[])),
  approved_by uuid REFERENCES auth.users(id),
  approved_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid NOT NULL REFERENCES auth.users(id),
  updated_by uuid REFERENCES auth.users(id),
  reference_number character varying,
  notes text,
  internal_notes text,
  terms_conditions text,
  CONSTRAINT purchase_orders_pkey PRIMARY KEY (id)
);

CREATE TABLE public.equipment_catalog (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  category character varying NOT NULL,
  subcategory character varying,
  brand character varying NOT NULL,
  model character varying NOT NULL,
  sku character varying NOT NULL UNIQUE,
  description text,
  specifications jsonb DEFAULT '{}'::jsonb,
  compatibility_info jsonb DEFAULT '{}'::jsonb,
  condition_rating character varying CHECK (condition_rating::text = ANY (ARRAY['New'::character varying, 'Excellent'::character varying, 'Good'::character varying, 'Fair'::character varying, 'Poor'::character varying]::text[])),
  requires_training boolean DEFAULT false,
  safety_requirements jsonb DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT equipment_catalog_pkey PRIMARY KEY (id)
);

CREATE TABLE public.parts_inventory (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  part_number character varying NOT NULL,
  description text NOT NULL,
  category character varying NOT NULL,
  subcategory character varying,
  primary_supplier character varying NOT NULL,
  supplier_part_number character varying,
  alternative_suppliers jsonb DEFAULT '[]'::jsonb,
  current_stock integer NOT NULL DEFAULT 0 CHECK (current_stock >= 0),
  minimum_stock integer NOT NULL DEFAULT 0 CHECK (minimum_stock >= 0),
  maximum_stock integer,
  reorder_point integer NOT NULL DEFAULT 0 CHECK (reorder_point >= 0),
  reorder_quantity integer NOT NULL DEFAULT 1 CHECK (reorder_quantity > 0),
  current_cost numeric NOT NULL CHECK (current_cost >= 0::numeric),
  average_cost numeric,
  last_purchase_cost numeric,
  location character varying,
  shelf_life integer,
  weight numeric,
  dimensions character varying,
  compatible_equipment text[],
  compatible_brands text[],
  is_active boolean DEFAULT true,
  is_obsolete boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT parts_inventory_pkey PRIMARY KEY (id)
);

CREATE TABLE public.purchase_order_lines (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  purchase_order_id uuid NOT NULL REFERENCES public.purchase_orders(id),
  line_number integer NOT NULL CHECK (line_number > 0),
  part_id uuid REFERENCES public.parts_inventory(id),
  equipment_id uuid REFERENCES public.equipment_catalog(id),
  description text NOT NULL,
  manufacturer_part_number character varying,
  vendor_part_number character varying,
  quantity_ordered numeric NOT NULL CHECK (quantity_ordered > 0::numeric),
  quantity_received numeric DEFAULT 0 CHECK (quantity_received >= 0::numeric),
  quantity_cancelled numeric DEFAULT 0 CHECK (quantity_cancelled >= 0::numeric),
  unit_of_measure character varying DEFAULT 'each'::character varying,
  unit_price numeric NOT NULL CHECK (unit_price >= 0::numeric),
  discount_percent numeric DEFAULT 0 CHECK (discount_percent >= 0::numeric AND discount_percent <= 100::numeric),
  line_total numeric NOT NULL DEFAULT 0,
  line_status character varying DEFAULT 'open'::character varying CHECK (line_status::text = ANY (ARRAY['open'::character varying, 'partially_received'::character varying, 'fully_received'::character varying, 'cancelled'::character varying, 'closed'::character varying]::text[])),
  required_date date,
  promised_date date,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  notes text,
  specifications jsonb,
  CONSTRAINT purchase_order_lines_pkey PRIMARY KEY (id)
);

CREATE TABLE public.goods_received_notes (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  grn_number character varying NOT NULL UNIQUE CHECK (grn_number::text ~ '^GRN[0-9]{6,10}$'::text),
  purchase_order_id uuid NOT NULL REFERENCES public.purchase_orders(id),
  vendor_id uuid NOT NULL REFERENCES public.vendors(id),
  received_date date NOT NULL DEFAULT CURRENT_DATE,
  received_by uuid NOT NULL REFERENCES auth.users(id),
  delivery_note_reference character varying,
  carrier_name character varying,
  tracking_number character varying,
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'received'::character varying, 'inspected'::character varying, 'accepted'::character varying, 'rejected'::character varying, 'partially_accepted'::character varying]::text[])),
  inspection_status character varying DEFAULT 'pending'::character varying CHECK (inspection_status::text = ANY (ARRAY['pending'::character varying, 'passed'::character varying, 'failed'::character varying, 'conditional'::character varying]::text[])),
  inspector_id uuid REFERENCES auth.users(id),
  inspection_date date,
  quality_notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  notes text,
  attachments jsonb,
  CONSTRAINT goods_received_notes_pkey PRIMARY KEY (id)
);

CREATE TABLE public.grn_line_items (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  grn_id uuid NOT NULL REFERENCES public.goods_received_notes(id),
  po_line_id uuid NOT NULL REFERENCES public.purchase_order_lines(id),
  line_number integer NOT NULL CHECK (line_number > 0),
  quantity_ordered numeric NOT NULL,
  quantity_received numeric NOT NULL CHECK (quantity_received >= 0::numeric),
  quantity_accepted numeric DEFAULT 0 CHECK (quantity_accepted >= 0::numeric),
  quantity_rejected numeric DEFAULT 0 CHECK (quantity_rejected >= 0::numeric),
  condition_received character varying DEFAULT 'good'::character varying CHECK (condition_received::text = ANY (ARRAY['good'::character varying, 'damaged'::character varying, 'defective'::character varying, 'incomplete'::character varying]::text[])),
  rejection_reason text,
  quality_grade character varying,
  location_received character varying,
  bin_location character varying,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  notes text,
  CONSTRAINT grn_line_items_pkey PRIMARY KEY (id)
);

CREATE TABLE public.stock_movements (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  part_id uuid NOT NULL REFERENCES public.parts_inventory(id),
  movement_type character varying NOT NULL CHECK (movement_type::text = ANY (ARRAY['In'::character varying, 'Out'::character varying, 'Transfer'::character varying, 'Adjustment'::character varying]::text[])),
  quantity integer NOT NULL CHECK (quantity <> 0),
  unit_cost numeric,
  total_cost numeric,
  reference_type character varying CHECK (reference_type::text = ANY (ARRAY['Purchase_Order'::character varying, 'Work_Order'::character varying, 'Stock_Adjustment'::character varying, 'Return'::character varying]::text[])),
  reference_id uuid,
  notes text,
  created_by uuid NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT stock_movements_pkey PRIMARY KEY (id)
);

-- Equipment, Rentals & Repairs
CREATE TABLE public.equipment_items (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  catalog_id uuid NOT NULL REFERENCES public.equipment_catalog(id),
  serial_number character varying,
  asset_tag character varying NOT NULL,
  purchase_date date,
  purchase_price numeric,
  current_value numeric,
  condition character varying NOT NULL DEFAULT 'Good'::character varying CHECK (condition::text = ANY (ARRAY['New'::character varying, 'Excellent'::character varying, 'Good'::character varying, 'Fair'::character varying, 'Poor'::character varying, 'Damaged'::character varying]::text[])),
  status character varying NOT NULL DEFAULT 'Available'::character varying CHECK (status::text = ANY (ARRAY['Available'::character varying, 'Rented'::character varying, 'In Repair'::character varying, 'Maintenance'::character varying, 'Retired'::character varying, 'Lost'::character varying, 'Stolen'::character varying]::text[])),
  location character varying,
  assigned_technician uuid REFERENCES public.employees(id),
  last_maintenance date,
  next_maintenance_due date,
  maintenance_history jsonb DEFAULT '[]'::jsonb,
  custom_attributes jsonb DEFAULT '{}'::jsonb,
  is_rentable boolean DEFAULT true,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT equipment_items_pkey PRIMARY KEY (id)
);

CREATE TABLE public.rental_rates (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  catalog_id uuid NOT NULL REFERENCES public.equipment_catalog(id),
  rate_type character varying NOT NULL DEFAULT 'Standard'::character varying CHECK (rate_type::text = ANY (ARRAY['Standard'::character varying, 'Premium'::character varying, 'Discount'::character varying, 'Corporate'::character varying, 'Student'::character varying, 'Event'::character varying]::text[])),
  hourly_rate numeric,
  daily_rate numeric,
  weekly_rate numeric,
  monthly_rate numeric,
  deposit_amount numeric,
  seasonal_adjustments jsonb DEFAULT '{}'::jsonb,
  volume_discounts jsonb DEFAULT '{}'::jsonb,
  effective_from date NOT NULL DEFAULT CURRENT_DATE,
  effective_to date,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT rental_rates_pkey PRIMARY KEY (id)
);

CREATE TABLE public.repair_templates (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  name character varying NOT NULL,
  description text,
  equipment_category character varying NOT NULL,
  common_issues text[],
  standard_procedures text[],
  estimated_time integer,
  required_skills text[],
  required_tools text[],
  safety_requirements text[],
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT repair_templates_pkey PRIMARY KEY (id)
);

CREATE TABLE public.repair_work_orders (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  work_order_number character varying NOT NULL,
  equipment_item_id uuid NOT NULL REFERENCES public.equipment_items(id),
  customer_id uuid REFERENCES public.customers(id),
  repair_type character varying NOT NULL CHECK (repair_type::text = ANY (ARRAY['Warranty'::character varying, 'Preventive'::character varying, 'Corrective'::character varying, 'Emergency'::character varying, 'Upgrade'::character varying, 'Inspection'::character varying, 'Customer_Damage'::character varying, 'Wear_and_Tear'::character varying]::text[])),
  priority character varying NOT NULL DEFAULT 'Normal'::character varying CHECK (priority::text = ANY (ARRAY['Low'::character varying, 'Normal'::character varying, 'High'::character varying, 'Urgent'::character varying, 'Emergency'::character varying]::text[])),
  status character varying NOT NULL DEFAULT 'Pending'::character varying CHECK (status::text = ANY (ARRAY['Pending'::character varying, 'Diagnosed'::character varying, 'Parts_Ordered'::character varying, 'In_Progress'::character varying, 'Testing'::character varying, 'Completed'::character varying, 'On_Hold'::character varying, 'Cancelled'::character varying, 'Parts_Unavailable'::character varying]::text[])),
  reported_issue text NOT NULL,
  initial_diagnosis text,
  final_diagnosis text,
  reported_date timestamp with time zone NOT NULL DEFAULT now(),
  scheduled_date timestamp with time zone,
  started_date timestamp with time zone,
  completed_date timestamp with time zone,
  estimated_completion timestamp with time zone,
  assigned_technician_id uuid REFERENCES public.employees(id),
  assigned_team_id uuid,
  estimated_cost numeric,
  actual_cost numeric,
  labor_cost numeric,
  parts_cost numeric,
  external_service_cost numeric,
  work_performed text,
  parts_used jsonb DEFAULT '[]'::jsonb,
  labor_entries jsonb DEFAULT '[]'::jsonb,
  test_results text,
  warranty_period integer,
  internal_notes text,
  customer_notes text,
  before_photos text[],
  after_photos text[],
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT repair_work_orders_pkey PRIMARY KEY (id)
);

-- Events & Project Management
CREATE TABLE public.venues (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  venue_name character varying NOT NULL,
  venue_type character varying NOT NULL CHECK (venue_type::text = ANY (ARRAY['CONCERT_HALL'::character varying, 'OUTDOOR_FESTIVAL'::character varying, 'CORPORATE_VENUE'::character varying, 'WEDDING_VENUE'::character varying, 'CONFERENCE_CENTER'::character varying, 'THEATRE'::character varying, 'CLUB'::character varying, 'ARENA'::character varying, 'STADIUM'::character varying, 'CHURCH'::character varying, 'HOTEL'::character varying, 'PRIVATE_RESIDENCE'::character varying]::text[])),
  address text NOT NULL,
  contact_information jsonb DEFAULT '{}'::jsonb,
  technical_specifications jsonb DEFAULT '{}'::jsonb,
  power_specifications jsonb DEFAULT '{}'::jsonb,
  rigging_specifications jsonb DEFAULT '{}'::jsonb,
  load_in_specifications jsonb DEFAULT '{}'::jsonb,
  restrictions_requirements jsonb DEFAULT '{}'::jsonb,
  load_in_rate numeric DEFAULT 0.00,
  security_deposit numeric DEFAULT 0.00,
  requires_insurance boolean DEFAULT false,
  insurance_requirements jsonb DEFAULT '{}'::jsonb,
  preferred_contact_method character varying DEFAULT 'email'::character varying,
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT venues_pkey PRIMARY KEY (id)
);

CREATE TABLE public.venue_specifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  venue_id uuid NOT NULL REFERENCES public.venues(id),
  specification_type character varying NOT NULL,
  specification_data jsonb DEFAULT '{}'::jsonb,
  notes text,
  last_updated date DEFAULT CURRENT_DATE,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT venue_specifications_pkey PRIMARY KEY (id)
);

CREATE TABLE public.load_in_procedures (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  venue_id uuid NOT NULL REFERENCES public.venues(id),
  procedure_type character varying NOT NULL,
  procedure_description text NOT NULL,
  estimated_duration integer,
  required_equipment jsonb DEFAULT '[]'::jsonb,
  safety_requirements jsonb DEFAULT '[]'::jsonb,
  contact_requirements text,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT load_in_procedures_pkey PRIMARY KEY (id)
);

CREATE TABLE public.event_projects (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  client_id uuid,
  venue_id uuid REFERENCES public.venues(id),
  project_number character varying NOT NULL,
  project_name character varying NOT NULL,
  event_type character varying NOT NULL CHECK (event_type::text = ANY (ARRAY['CONCERT'::character varying, 'CORPORATE_EVENT'::character varying, 'FESTIVAL'::character varying, 'WEDDING'::character varying, 'PRIVATE_EVENT'::character varying, 'CONFERENCE'::character varying, 'SEMINAR'::character varying, 'THEATRE'::character varying, 'BROADCAST'::character varying, 'CLUB_NIGHT'::character varying, 'OUTDOOR_EVENT'::character varying, 'RECORDING_SESSION'::character varying]::text[])),
  production_scale character varying NOT NULL CHECK (production_scale::text = ANY (ARRAY['SMALL'::character varying, 'MEDIUM'::character varying, 'LARGE'::character varying, 'FESTIVAL'::character varying]::text[])),
  event_date date NOT NULL,
  doors_open time without time zone,
  sound_check time without time zone,
  show_start time without time zone,
  show_end time without time zone,
  load_out_complete time without time zone,
  status character varying NOT NULL DEFAULT 'INQUIRY'::character varying CHECK (status::text = ANY (ARRAY['INQUIRY'::character varying, 'QUOTED'::character varying, 'CONFIRMED'::character varying, 'IN_PLANNING'::character varying, 'READY_TO_DEPLOY'::character varying, 'IN_PROGRESS'::character varying, 'COMPLETED'::character varying, 'CANCELLED'::character varying, 'ON_HOLD'::character varying]::text[])),
  budget_amount numeric DEFAULT 0.00,
  actual_cost numeric DEFAULT 0.00,
  technical_requirements jsonb DEFAULT '{}'::jsonb,
  artist_requirements jsonb DEFAULT '{}'::jsonb,
  special_notes text,
  project_manager_id uuid,
  lead_engineer_id uuid,
  requires_rigging boolean DEFAULT false,
  requires_power_distribution boolean DEFAULT false,
  has_broadcast_feed boolean DEFAULT false,
  weather_contingency jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT event_projects_pkey PRIMARY KEY (id)
);

CREATE TABLE public.crew_members (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  employee_id uuid,
  crew_role character varying NOT NULL CHECK (crew_role::text = ANY (ARRAY['PRODUCTION_MANAGER'::character varying, 'AUDIO_ENGINEER'::character varying, 'MONITOR_ENGINEER'::character varying, 'SYSTEM_TECHNICIAN'::character varying, 'RIGGER'::character varying, 'LIGHTING_TECHNICIAN'::character varying, 'VIDEO_TECHNICIAN'::character varying, 'STAGE_HAND'::character varying, 'TRUCK_DRIVER'::character varying, 'SECURITY'::character varying, 'RUNNER'::character varying]::text[])),
  experience_level character varying NOT NULL CHECK (experience_level::text = ANY (ARRAY['JUNIOR'::character varying, 'INTERMEDIATE'::character varying, 'SENIOR'::character varying, 'EXPERT'::character varying]::text[])),
  specializations jsonb DEFAULT '[]'::jsonb,
  certifications jsonb DEFAULT '[]'::jsonb,
  equipment_proficiency jsonb DEFAULT '[]'::jsonb,
  hourly_rate numeric DEFAULT 0.00,
  overtime_rate numeric DEFAULT 0.00,
  available_weekends boolean DEFAULT true,
  available_travel boolean DEFAULT true,
  availability_restrictions jsonb DEFAULT '{}'::jsonb,
  union_status character varying DEFAULT 'NON_UNION'::character varying,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT crew_members_pkey PRIMARY KEY (id)
);

CREATE TABLE public.crew_certifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  crew_member_id uuid NOT NULL REFERENCES public.crew_members(id),
  certification_type character varying NOT NULL,
  certification_name character varying NOT NULL,
  issuing_body character varying,
  issue_date date,
  expiry_date date,
  certificate_number character varying,
  is_current boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT crew_certifications_pkey PRIMARY KEY (id)
);

CREATE TABLE public.crew_assignments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  event_project_id uuid NOT NULL REFERENCES public.event_projects(id),
  crew_member_id uuid NOT NULL REFERENCES public.crew_members(id),
  assignment_role character varying NOT NULL CHECK (assignment_role::text = ANY (ARRAY['PRODUCTION_MANAGER'::character varying, 'AUDIO_ENGINEER'::character varying, 'MONITOR_ENGINEER'::character varying, 'SYSTEM_TECHNICIAN'::character varying, 'RIGGER'::character varying, 'LIGHTING_TECHNICIAN'::character varying, 'VIDEO_TECHNICIAN'::character varying, 'STAGE_HAND'::character varying, 'TRUCK_DRIVER'::character varying, 'SECURITY'::character varying, 'RUNNER'::character varying]::text[])),
  shift_type character varying NOT NULL CHECK (shift_type::text = ANY (ARRAY['LOAD_OUT'::character varying, 'SETUP'::character varying, 'SOUND_CHECK'::character varying, 'SHOW'::character varying, 'STRIKE'::character varying, 'LOAD_IN'::character varying, 'FULL_DAY'::character varying, 'SPLIT_SHIFT'::character varying]::text[])),
  call_time timestamp with time zone,
  scheduled_finish timestamp with time zone,
  actual_start timestamp with time zone,
  actual_finish timestamp with time zone,
  hours_worked numeric DEFAULT 0.00,
  overtime_hours numeric DEFAULT 0.00,
  travel_time numeric DEFAULT 0.00,
  status character varying NOT NULL DEFAULT 'SCHEDULED'::character varying CHECK (status::text = ANY (ARRAY['SCHEDULED'::character varying, 'CONFIRMED'::character varying, 'IN_PROGRESS'::character varying, 'COMPLETED'::character varying, 'NO_SHOW'::character varying, 'CANCELLED'::character varying]::text[])),
  assignment_notes text,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT crew_assignments_pkey PRIMARY KEY (id)
);

CREATE TABLE public.assignment_tasks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  crew_assignment_id uuid NOT NULL REFERENCES public.crew_assignments(id),
  task_name character varying NOT NULL,
  task_category character varying,
  task_start timestamp with time zone,
  task_end timestamp with time zone,
  status character varying NOT NULL DEFAULT 'PENDING'::character varying CHECK (status::text = ANY (ARRAY['PENDING'::character varying, 'IN_PROGRESS'::character varying, 'COMPLETED'::character varying, 'CANCELLED'::character varying, 'BLOCKED'::character varying]::text[])),
  task_notes text,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT assignment_tasks_pkey PRIMARY KEY (id)
);

CREATE TABLE public.time_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  crew_assignment_id uuid NOT NULL REFERENCES public.crew_assignments(id),
  crew_member_id uuid NOT NULL REFERENCES public.crew_members(id),
  clock_in timestamp with time zone NOT NULL,
  clock_out timestamp with time zone,
  break_duration numeric DEFAULT 0.00,
  total_hours numeric DEFAULT 0.00,
  log_type character varying DEFAULT 'REGULAR'::character varying CHECK (log_type::text = ANY (ARRAY['REGULAR'::character varying, 'OVERTIME'::character varying, 'TRAVEL'::character varying, 'BREAK'::character varying, 'MEAL'::character varying]::text[])),
  notes text,
  approved_by uuid,
  approved_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT time_logs_pkey PRIMARY KEY (id)
);

-- Training & Education
CREATE TABLE public.training_courses (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  title character varying NOT NULL,
  description text,
  course_type public.course_type NOT NULL,
  category public.course_category NOT NULL,
  difficulty_level public.difficulty_level NOT NULL,
  price numeric,
  access_model public.access_model NOT NULL,
  estimated_duration_hours integer,
  is_certification_course boolean DEFAULT false,
  preview_video_url text,
  thumbnail_url text,
  learning_objectives text[],
  prerequisites text[],
  metadata jsonb DEFAULT '{}'::jsonb,
  is_published boolean DEFAULT false,
  published_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT training_courses_pkey PRIMARY KEY (id)
);

CREATE TABLE public.course_modules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  course_id uuid NOT NULL REFERENCES public.training_courses(id),
  title character varying NOT NULL,
  description text,
  order_index integer NOT NULL,
  estimated_duration_minutes integer,
  learning_objectives text[],
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT course_modules_pkey PRIMARY KEY (id)
);

CREATE TABLE public.lessons (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  module_id uuid NOT NULL REFERENCES public.course_modules(id),
  title character varying NOT NULL,
  description text,
  lesson_type public.lesson_type NOT NULL,
  order_index integer NOT NULL,
  estimated_duration_minutes integer,
  is_required boolean DEFAULT true,
  prerequisites uuid[] DEFAULT '{}'::uuid[],
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT lessons_pkey PRIMARY KEY (id)
);

CREATE TABLE public.content_items (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  lesson_id uuid NOT NULL REFERENCES public.lessons(id),
  content_type public.content_type NOT NULL,
  title character varying NOT NULL,
  description text,
  content_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  file_url text,
  duration_seconds integer,
  order_index integer NOT NULL,
  is_downloadable boolean DEFAULT false,
  access_requirements jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT content_items_pkey PRIMARY KEY (id)
);

CREATE TABLE public.course_prerequisites (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  course_id uuid NOT NULL REFERENCES public.training_courses(id),
  prerequisite_course_id uuid NOT NULL REFERENCES public.training_courses(id),
  is_required boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT course_prerequisites_pkey PRIMARY KEY (id)
);

-- Brand & Marketing
CREATE TABLE public.brands (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  description text,
  slug character varying NOT NULL UNIQUE CHECK (slug::text ~ '^[a-z0-9-]+$'::text),
  logo_url text,
  primary_color character varying,
  secondary_color character varying,
  accent_color character varying,
  font_family character varying,
  brand_voice text,
  brand_values jsonb DEFAULT '[]'::jsonb,
  target_audience jsonb DEFAULT '{}'::jsonb,
  brand_positioning text,
  mission_statement text,
  vision_statement text,
  tagline character varying,
  website_url text,
  industry character varying,
  brand_tier character varying CHECK (brand_tier::text = ANY (ARRAY['premium'::character varying, 'mid-market'::character varying, 'budget'::character varying, 'luxury'::character varying]::text[])),
  status character varying DEFAULT 'active'::character varying CHECK (status::text = ANY (ARRAY['active'::character varying, 'inactive'::character varying, 'archived'::character varying, 'developing'::character varying]::text[])),
  is_primary boolean DEFAULT false,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT brands_pkey PRIMARY KEY (id)
);

CREATE TABLE public.brand_assets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  brand_id uuid NOT NULL REFERENCES public.brands(id),
  name character varying NOT NULL,
  description text,
  asset_type character varying NOT NULL CHECK (asset_type::text = ANY (ARRAY['logo'::character varying, 'icon'::character varying, 'image'::character varying, 'template'::character varying, 'document'::character varying, 'video'::character varying, 'audio'::character varying, 'font'::character varying, 'color_palette'::character varying, 'style_guide'::character varying, 'presentation'::character varying, 'other'::character varying]::text[])),
  file_url text NOT NULL,
  file_name character varying NOT NULL,
  file_size bigint CHECK (file_size IS NULL OR file_size > 0),
  mime_type character varying,
  file_extension character varying,
  dimensions jsonb,
  usage_rights character varying CHECK (usage_rights::text = ANY (ARRAY['exclusive'::character varying, 'non_exclusive'::character varying, 'limited'::character varying, 'public_domain'::character varying, 'licensed'::character varying]::text[])),
  usage_guidelines text,
  tags text[] DEFAULT '{}'::text[],
  categories text[] DEFAULT '{}'::text[],
  version character varying DEFAULT '1.0'::character varying,
  is_approved boolean DEFAULT false,
  approval_date timestamp with time zone,
  approved_by uuid REFERENCES auth.users(id),
  download_count integer DEFAULT 0 CHECK (download_count >= 0),
  last_downloaded_at timestamp with time zone,
  is_archived boolean DEFAULT false,
  archived_at timestamp with time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT brand_assets_pkey PRIMARY KEY (id)
);

CREATE TABLE public.brand_guidelines (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  brand_id uuid NOT NULL REFERENCES public.brands(id),
  title character varying NOT NULL,
  description text,
  guideline_type character varying NOT NULL CHECK (guideline_type::text = ANY (ARRAY['logo_usage'::character varying, 'color_palette'::character varying, 'typography'::character varying, 'imagery'::character varying, 'voice_tone'::character varying, 'messaging'::character varying, 'layout'::character varying, 'social_media'::character varying, 'email'::character varying, 'web'::character varying, 'print'::character varying, 'packaging'::character varying, 'advertising'::character varying, 'presentation'::character varying, 'video'::character varying, 'photography'::character varying, 'general'::character varying]::text[])),
  content jsonb NOT NULL,
  rules text[],
  do_examples jsonb DEFAULT '[]'::jsonb,
  dont_examples jsonb DEFAULT '[]'::jsonb,
  compliance_level character varying DEFAULT 'required'::character varying CHECK (compliance_level::text = ANY (ARRAY['required'::character varying, 'recommended'::character varying, 'optional'::character varying]::text[])),
  applies_to text[] DEFAULT '{}'::text[],
  version character varying DEFAULT '1.0'::character varying,
  is_active boolean DEFAULT true,
  effective_date timestamp with time zone DEFAULT now(),
  expiry_date timestamp with time zone,
  approval_required boolean DEFAULT false,
  approved_by uuid REFERENCES auth.users(id),
  approval_date timestamp with time zone,
  review_frequency integer CHECK (review_frequency IS NULL OR review_frequency > 0),
  last_reviewed_at timestamp with time zone,
  next_review_date timestamp with time zone,
  related_assets uuid[],
  external_references jsonb DEFAULT '[]'::jsonb,
  tags text[] DEFAULT '{}'::text[],
  priority integer DEFAULT 100 CHECK (priority > 0),
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT brand_guidelines_pkey PRIMARY KEY (id)
);

CREATE TABLE public.brand_campaigns (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  brand_id uuid NOT NULL REFERENCES public.brands(id),
  name character varying NOT NULL,
  description text,
  campaign_type character varying NOT NULL CHECK (campaign_type::text = ANY (ARRAY['awareness'::character varying, 'engagement'::character varying, 'conversion'::character varying, 'retention'::character varying, 'rebranding'::character varying, 'product_launch'::character varying, 'seasonal'::character varying, 'event'::character varying, 'social_media'::character varying, 'email'::character varying, 'content'::character varying, 'influencer'::character varying, 'paid_advertising'::character varying, 'pr'::character varying, 'other'::character varying]::text[])),
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'planning'::character varying, 'approved'::character varying, 'active'::character varying, 'paused'::character varying, 'completed'::character varying, 'cancelled'::character varying]::text[])),
  priority character varying DEFAULT 'medium'::character varying CHECK (priority::text = ANY (ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'critical'::character varying]::text[])),
  budget_allocated numeric CHECK (budget_allocated IS NULL OR budget_allocated >= 0::numeric),
  budget_spent numeric DEFAULT 0 CHECK (budget_spent >= 0::numeric),
  currency character varying DEFAULT 'USD'::character varying CHECK (currency::text ~ '^[A-Z]{3}$'::text),
  target_audience jsonb DEFAULT '{}'::jsonb,
  campaign_goals jsonb DEFAULT '[]'::jsonb,
  kpis jsonb DEFAULT '[]'::jsonb,
  channels text[] DEFAULT '{}'::text[],
  start_date timestamp with time zone,
  end_date timestamp with time zone,
  launch_date timestamp with time zone,
  related_assets uuid[],
  related_guidelines uuid[],
  creative_brief text,
  messaging jsonb DEFAULT '{}'::jsonb,
  performance_metrics jsonb DEFAULT '{}'::jsonb,
  conversion_tracking jsonb DEFAULT '{}'::jsonb,
  approval_workflow jsonb DEFAULT '[]'::jsonb,
  team_members jsonb DEFAULT '[]'::jsonb,
  external_vendors jsonb DEFAULT '[]'::jsonb,
  assets_created uuid[],
  content_calendar jsonb DEFAULT '[]'::jsonb,
  notes text,
  tags text[] DEFAULT '{}'::text[],
  is_template boolean DEFAULT false,
  template_category character varying,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT brand_campaigns_pkey PRIMARY KEY (id)
);

CREATE TABLE public.marketing_campaigns (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  campaign_type character varying NOT NULL CHECK (campaign_type::text = ANY (ARRAY['brand_awareness'::character varying, 'lead_generation'::character varying, 'customer_acquisition'::character varying, 'customer_retention'::character varying, 'product_launch'::character varying, 'event_promotion'::character varying, 'seasonal'::character varying, 'content_marketing'::character varying, 'email_marketing'::character varying, 'social_media'::character varying, 'influencer'::character varying, 'paid_advertising'::character varying, 'seo'::character varying, 'affiliate'::character varying, 'referral'::character varying, 'partnership'::character varying, 'remarketing'::character varying, 'other'::character varying]::text[])),
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'planning'::character varying, 'approved'::character varying, 'active'::character varying, 'paused'::character varying, 'completed'::character varying, 'cancelled'::character varying, 'archived'::character varying]::text[])),
  priority character varying DEFAULT 'medium'::character varying CHECK (priority::text = ANY (ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'critical'::character varying]::text[])),
  budget_allocated numeric CHECK (budget_allocated IS NULL OR budget_allocated >= 0::numeric),
  budget_spent numeric DEFAULT 0 CHECK (budget_spent >= 0::numeric),
  currency character varying DEFAULT 'USD'::character varying,
  start_date date,
  end_date date,
  launch_date date,
  target_audience jsonb DEFAULT '{}'::jsonb,
  goals jsonb DEFAULT '[]'::jsonb,
  kpis jsonb DEFAULT '[]'::jsonb,
  channels jsonb DEFAULT '[]'::jsonb,
  messaging jsonb DEFAULT '{}'::jsonb,
  impressions integer DEFAULT 0,
  reach integer DEFAULT 0,
  clicks integer DEFAULT 0,
  ctr numeric DEFAULT 0,
  conversions integer DEFAULT 0,
  conversion_rate numeric DEFAULT 0,
  cost_per_click numeric DEFAULT 0,
  cost_per_acquisition numeric DEFAULT 0,
  cost_per_lead numeric DEFAULT 0,
  cost_per_thousand_impressions numeric DEFAULT 0,
  engagement_rate numeric DEFAULT 0,
  bounce_rate numeric DEFAULT 0,
  time_on_page integer DEFAULT 0,
  page_views integer DEFAULT 0,
  unique_visitors integer DEFAULT 0,
  leads_generated integer DEFAULT 0,
  qualified_leads integer DEFAULT 0,
  revenue numeric DEFAULT 0,
  roi numeric DEFAULT 0,
  roas numeric DEFAULT 0,
  lifetime_value numeric DEFAULT 0,
  brand_awareness_lift numeric DEFAULT 0,
  share_of_voice numeric DEFAULT 0,
  social_shares integer DEFAULT 0,
  comments integer DEFAULT 0,
  likes integer DEFAULT 0,
  follows integer DEFAULT 0,
  unsubscribes integer DEFAULT 0,
  spam_complaints integer DEFAULT 0,
  deliverability_rate numeric DEFAULT 0,
  open_rate numeric DEFAULT 0,
  click_through_rate numeric DEFAULT 0,
  metrics_last_updated timestamp with time zone,
  conversion_tracking jsonb DEFAULT '{}'::jsonb,
  utm_source character varying,
  utm_medium character varying,
  utm_campaign character varying,
  utm_term character varying,
  utm_content character varying,
  approval_workflow jsonb DEFAULT '[]'::jsonb,
  approval_status character varying DEFAULT 'draft'::character varying CHECK (approval_status::text = ANY (ARRAY['draft'::character varying, 'pending_review'::character varying, 'approved'::character varying, 'rejected'::character varying, 'changes_requested'::character varying]::text[])),
  approved_by uuid REFERENCES auth.users(id),
  approved_at timestamp with time zone,
  team_members jsonb DEFAULT '[]'::jsonb,
  external_vendors jsonb DEFAULT '[]'::jsonb,
  related_assets jsonb DEFAULT '[]'::jsonb,
  content_calendar jsonb DEFAULT '[]'::jsonb,
  creative_brief text,
  notes text,
  tags jsonb DEFAULT '[]'::jsonb,
  is_template boolean DEFAULT false,
  template_category character varying,
  custom_metrics jsonb DEFAULT '{}'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT marketing_campaigns_pkey PRIMARY KEY (id)
);

CREATE TABLE public.marketing_leads (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  first_name character varying,
  last_name character varying,
  email character varying CHECK (email IS NULL OR email::text ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text),
  phone character varying,
  company character varying,
  job_title character varying,
  industry character varying,
  website text,
  lead_source character varying NOT NULL CHECK (lead_source::text = ANY (ARRAY['website'::character varying, 'social_media'::character varying, 'email_campaign'::character varying, 'paid_advertising'::character varying, 'organic_search'::character varying, 'referral'::character varying, 'event'::character varying, 'webinar'::character varying, 'content_download'::character varying, 'newsletter'::character varying, 'direct'::character varying, 'phone'::character varying, 'trade_show'::character varying, 'partner'::character varying, 'affiliate'::character varying, 'cold_outreach'::character varying, 'other'::character varying]::text[])),
  lead_status character varying DEFAULT 'new'::character varying CHECK (lead_status::text = ANY (ARRAY['new'::character varying, 'contacted'::character varying, 'qualified'::character varying, 'unqualified'::character varying, 'opportunity'::character varying, 'customer'::character varying, 'lost'::character varying, 'inactive'::character varying]::text[])),
  lead_score integer DEFAULT 0 CHECK (lead_score >= 0 AND lead_score <= 1000),
  qualification_status character varying DEFAULT 'unqualified'::character varying CHECK (qualification_status::text = ANY (ARRAY['unqualified'::character varying, 'marketing_qualified'::character varying, 'sales_qualified'::character varying, 'sales_accepted'::character varying, 'customer'::character varying]::text[])),
  assigned_to uuid REFERENCES auth.users(id),
  assigned_at timestamp with time zone,
  campaign_id uuid REFERENCES public.marketing_campaigns(id),
  utm_source character varying,
  utm_medium character varying,
  utm_campaign character varying,
  utm_term character varying,
  utm_content character varying,
  first_touch_date timestamp with time zone NOT NULL DEFAULT now(),
  last_touch_date timestamp with time zone NOT NULL DEFAULT now(),
  conversion_date timestamp with time zone,
  conversion_value numeric,
  lifetime_value numeric,
  interests jsonb DEFAULT '[]'::jsonb,
  behaviors jsonb DEFAULT '[]'::jsonb,
  demographics jsonb DEFAULT '{}'::jsonb,
  custom_fields jsonb DEFAULT '{}'::jsonb,
  interactions jsonb DEFAULT '[]'::jsonb,
  notes jsonb DEFAULT '[]'::jsonb,
  tags jsonb DEFAULT '[]'::jsonb,
  do_not_contact boolean DEFAULT false,
  unsubscribed boolean DEFAULT false,
  email_verified boolean DEFAULT false,
  phone_verified boolean DEFAULT false,
  gdpr_consent boolean DEFAULT false,
  consent_date timestamp with time zone,
  demographic_score integer DEFAULT 0,
  behavioral_score integer DEFAULT 0,
  engagement_score integer DEFAULT 0,
  firmographic_score integer DEFAULT 0,
  score_last_updated timestamp with time zone DEFAULT now(),
  preferred_contact_method character varying CHECK (preferred_contact_method::text = ANY (ARRAY['email'::character varying, 'phone'::character varying, 'sms'::character varying, 'linkedin'::character varying, 'other'::character varying]::text[])),
  preferred_contact_time character varying,
  timezone character varying,
  language character varying DEFAULT 'en'::character varying,
  temperature character varying DEFAULT 'cold'::character varying CHECK (temperature::text = ANY (ARRAY['hot'::character varying, 'warm'::character varying, 'cold'::character varying]::text[])),
  last_activity_date timestamp with time zone,
  activity_frequency integer DEFAULT 0 CHECK (activity_frequency >= 0),
  referral_source character varying,
  referrer_name character varying,
  original_landing_page text,
  current_stage character varying,
  probability_to_close numeric CHECK (probability_to_close IS NULL OR probability_to_close >= 0::numeric AND probability_to_close <= 1::numeric),
  expected_close_date date,
  deal_size numeric,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT marketing_leads_pkey PRIMARY KEY (id)
);

CREATE TABLE public.marketing_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid,
  default_currency character varying DEFAULT 'ZAR'::character varying,
  default_timezone character varying DEFAULT 'UTC'::character varying,
  lead_scoring_enabled boolean DEFAULT true,
  lead_scoring_model jsonb DEFAULT '{}'::jsonb,
  mql_threshold integer DEFAULT 50,
  sql_threshold integer DEFAULT 75,
  score_decay_enabled boolean DEFAULT false,
  score_decay_period integer DEFAULT 30,
  score_decay_percentage numeric DEFAULT 0.10,
  attribution_model character varying DEFAULT 'last_touch'::character varying CHECK (attribution_model::text = ANY (ARRAY['first_touch'::character varying, 'last_touch'::character varying, 'linear'::character varying, 'time_decay'::character varying, 'position_based'::character varying, 'custom'::character varying]::text[])),
  attribution_window integer DEFAULT 30 CHECK (attribution_window > 0),
  cross_device_tracking boolean DEFAULT false,
  automation_enabled boolean DEFAULT true,
  lead_routing_enabled boolean DEFAULT false,
  lead_routing_rules jsonb DEFAULT '[]'::jsonb,
  email_automation_enabled boolean DEFAULT false,
  social_media_auto_posting boolean DEFAULT false,
  optimal_timing_enabled boolean DEFAULT false,
  content_curation_enabled boolean DEFAULT false,
  auto_hashtag_suggestions boolean DEFAULT true,
  cross_platform_posting boolean DEFAULT false,
  social_media_approval_required boolean DEFAULT true,
  max_posts_per_day integer DEFAULT 5 CHECK (max_posts_per_day > 0),
  posting_schedule jsonb DEFAULT '{}'::jsonb,
  email_automation jsonb DEFAULT '{}'::jsonb,
  default_sender_name character varying,
  default_sender_email character varying,
  bounce_handling_enabled boolean DEFAULT true,
  unsubscribe_handling boolean DEFAULT true,
  email_notifications boolean DEFAULT true,
  push_notifications boolean DEFAULT true,
  slack_notifications boolean DEFAULT false,
  webhook_notifications boolean DEFAULT false,
  notification_settings jsonb DEFAULT '{"daily_digest": false, "weekly_summary": true, "campaign_updates": true, "approval_requests": true, "lead_notifications": true, "performance_alerts": true}'::jsonb,
  crm_integration_enabled boolean DEFAULT false,
  crm_integration_settings jsonb DEFAULT '{}'::jsonb,
  analytics_integration jsonb DEFAULT '{}'::jsonb,
  advertising_integration jsonb DEFAULT '{}'::jsonb,
  reporting_automation boolean DEFAULT false,
  daily_reports boolean DEFAULT false,
  weekly_reports boolean DEFAULT true,
  monthly_reports boolean DEFAULT true,
  real_time_alerts boolean DEFAULT true,
  performance_threshold_alerts boolean DEFAULT true,
  gdpr_compliance_enabled boolean DEFAULT true,
  data_retention_period integer DEFAULT 365 CHECK (data_retention_period > 0),
  anonymize_data_after_retention boolean DEFAULT true,
  consent_tracking_enabled boolean DEFAULT true,
  custom_fields_config jsonb DEFAULT '[]'::jsonb,
  approval_workflows jsonb DEFAULT '[]'::jsonb,
  custom_integrations jsonb DEFAULT '{}'::jsonb,
  api_rate_limits jsonb DEFAULT '{"requests_per_day": 10000, "requests_per_hour": 1000, "requests_per_minute": 100}'::jsonb,
  webhook_settings jsonb DEFAULT '[]'::jsonb,
  require_approval_for_campaigns boolean DEFAULT true,
  require_approval_for_posts boolean DEFAULT true,
  require_approval_for_budget_changes boolean DEFAULT true,
  max_campaign_budget numeric,
  max_daily_spend numeric,
  feature_flags jsonb DEFAULT '{"ai_optimization": false, "social_listening": false, "advanced_analytics": true, "predictive_scoring": false, "competitor_analysis": false, "marketing_automation": true, "advanced_segmentation": true}'::jsonb,
  brand_colors jsonb DEFAULT '{}'::jsonb,
  custom_dashboard_config jsonb DEFAULT '{}'::jsonb,
  default_chart_types jsonb DEFAULT '[]'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT marketing_settings_pkey PRIMARY KEY (id)
);

CREATE TABLE public.social_media_accounts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  platform character varying NOT NULL CHECK (platform::text = ANY (ARRAY['facebook'::character varying, 'instagram'::character varying, 'twitter'::character varying, 'linkedin'::character varying, 'tiktok'::character varying, 'youtube'::character varying, 'pinterest'::character varying, 'snapchat'::character varying, 'reddit'::character varying, 'discord'::character varying, 'telegram'::character varying, 'whatsapp'::character varying, 'threads'::character varying, 'other'::character varying]::text[])),
  account_name character varying NOT NULL,
  account_handle character varying,
  account_id character varying,
  access_token text,
  refresh_token text,
  token_expires_at timestamp with time zone,
  account_type character varying DEFAULT 'business'::character varying CHECK (account_type::text = ANY (ARRAY['personal'::character varying, 'business'::character varying, 'creator'::character varying]::text[])),
  followers_count integer DEFAULT 0 CHECK (followers_count >= 0),
  following_count integer DEFAULT 0 CHECK (following_count >= 0),
  posts_count integer DEFAULT 0 CHECK (posts_count >= 0),
  is_verified boolean DEFAULT false,
  is_active boolean DEFAULT true,
  is_connected boolean DEFAULT false,
  last_sync_at timestamp with time zone,
  profile_image_url text,
  cover_image_url text,
  bio text,
  website text,
  location character varying,
  contact_email character varying,
  phone_number character varying,
  business_category character varying,
  permissions jsonb DEFAULT '[]'::jsonb,
  rate_limits jsonb DEFAULT '{}'::jsonb,
  api_version character varying,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT social_media_accounts_pkey PRIMARY KEY (id)
);

CREATE TABLE public.social_media_posts (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  account_id uuid NOT NULL REFERENCES public.social_media_accounts(id),
  platform character varying NOT NULL CHECK (platform::text = ANY (ARRAY['facebook'::character varying, 'instagram'::character varying, 'twitter'::character varying, 'linkedin'::character varying, 'tiktok'::character varying, 'youtube'::character varying, 'pinterest'::character varying, 'snapchat'::character varying, 'reddit'::character varying, 'discord'::character varying, 'telegram'::character varying, 'whatsapp'::character varying, 'threads'::character varying, 'other'::character varying]::text[])),
  platform_post_id character varying,
  content_type character varying DEFAULT 'post'::character varying CHECK (content_type::text = ANY (ARRAY['post'::character varying, 'story'::character varying, 'reel'::character varying, 'video'::character varying, 'image'::character varying, 'carousel'::character varying, 'poll'::character varying, 'live'::character varying, 'event'::character varying, 'article'::character varying, 'ad'::character varying]::text[])),
  caption text,
  media_urls jsonb DEFAULT '[]'::jsonb,
  media_types jsonb DEFAULT '[]'::jsonb,
  hashtags jsonb DEFAULT '[]'::jsonb,
  mentions jsonb DEFAULT '[]'::jsonb,
  location character varying,
  link text,
  call_to_action character varying,
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'scheduled'::character varying, 'published'::character varying, 'failed'::character varying, 'cancelled'::character varying, 'archived'::character varying]::text[])),
  scheduled_at timestamp with time zone,
  published_at timestamp with time zone,
  failed_at timestamp with time zone,
  failure_reason text,
  approval_status character varying DEFAULT 'draft'::character varying CHECK (approval_status::text = ANY (ARRAY['draft'::character varying, 'pending_review'::character varying, 'approved'::character varying, 'rejected'::character varying, 'published'::character varying]::text[])),
  approved_by uuid REFERENCES auth.users(id),
  approved_at timestamp with time zone,
  campaign_id uuid REFERENCES public.brand_campaigns(id),
  likes_count integer DEFAULT 0,
  comments_count integer DEFAULT 0,
  shares_count integer DEFAULT 0,
  saves_count integer DEFAULT 0,
  views_count integer DEFAULT 0,
  reach integer DEFAULT 0,
  impressions integer DEFAULT 0,
  clicks integer DEFAULT 0,
  click_through_rate numeric DEFAULT 0,
  engagement_rate numeric DEFAULT 0,
  video_views integer DEFAULT 0,
  profile_visits integer DEFAULT 0,
  website_clicks integer DEFAULT 0,
  metrics_last_updated timestamp with time zone,
  audience_targeting jsonb DEFAULT '{}'::jsonb,
  boosted boolean DEFAULT false,
  boost_budget numeric CHECK (boost_budget IS NULL OR boost_budget >= 0::numeric),
  boost_duration integer CHECK (boost_duration IS NULL OR boost_duration > 0),
  boost_start_date timestamp with time zone,
  boost_end_date timestamp with time zone,
  notes text,
  tags jsonb DEFAULT '[]'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT social_media_posts_pkey PRIMARY KEY (id)
);

-- System, Governance & User Management
CREATE TABLE public.modules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  display_name character varying NOT NULL,
  description text,
  icon character varying,
  route_path character varying,
  is_active boolean DEFAULT true,
  requires_permissions jsonb DEFAULT '[]'::jsonb,
  parent_module_id uuid REFERENCES public.modules(id),
  sort_order integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT modules_pkey PRIMARY KEY (id)
);

CREATE TABLE public.profiles (
  id uuid NOT NULL REFERENCES auth.users(id),
  email character varying NOT NULL UNIQUE,
  username character varying,
  role character varying DEFAULT 'user'::character varying,
  permissions text[] DEFAULT '{}'::text[],
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  first_name text,
  last_name text,
  display_name text,
  phone text,
  avatar_url text,
  department text,
  job_title text,
  is_verified boolean DEFAULT false,
  last_login_at timestamp with time zone,
  CONSTRAINT profiles_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_profiles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE REFERENCES auth.users(id),
  username character varying NOT NULL UNIQUE,
  first_name character varying,
  last_name character varying,
  email character varying NOT NULL,
  phone character varying,
  avatar_url text,
  bio text,
  is_active boolean DEFAULT true,
  is_verified boolean DEFAULT false,
  last_login_at timestamp with time zone,
  password_changed_at timestamp with time zone,
  failed_login_attempts integer DEFAULT 0,
  locked_until timestamp with time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_profiles_pkey PRIMARY KEY (id)
);

CREATE TABLE public.roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  description text,
  permissions text[] DEFAULT '{}'::text[],
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT roles_pkey PRIMARY KEY (id)
);

CREATE TABLE public.permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL UNIQUE,
  display_name character varying NOT NULL,
  description text,
  module_name character varying NOT NULL,
  resource_type character varying NOT NULL,
  action character varying NOT NULL,
  is_system_permission boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  module text,
  CONSTRAINT permissions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.role_permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  role_id uuid NOT NULL REFERENCES public.roles(id),
  permission_id uuid NOT NULL REFERENCES public.permissions(id),
  granted_by uuid REFERENCES auth.users(id),
  granted_at timestamp with time zone DEFAULT now(),
  CONSTRAINT role_permissions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_roles (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  role character varying NOT NULL,
  assigned_at timestamp with time zone DEFAULT now(),
  assigned_by uuid REFERENCES auth.users(id),
  CONSTRAINT user_roles_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_permissions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  permission_id uuid NOT NULL REFERENCES public.permissions(id),
  granted boolean DEFAULT true,
  granted_by uuid REFERENCES auth.users(id),
  granted_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone,
  reason text,
  is_active boolean DEFAULT true,
  CONSTRAINT user_permissions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_module_access (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  module_id uuid NOT NULL REFERENCES public.modules(id),
  access_granted boolean DEFAULT true,
  granted_by uuid REFERENCES auth.users(id),
  granted_at timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone,
  reason text,
  CONSTRAINT user_module_access_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_preferences (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  module character varying NOT NULL,
  preference_key character varying NOT NULL,
  preference_value jsonb NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_preferences_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_sessions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  session_token character varying NOT NULL UNIQUE,
  ip_address inet,
  user_agent text,
  is_active boolean DEFAULT true,
  last_activity timestamp with time zone DEFAULT now(),
  expires_at timestamp with time zone NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_sessions_pkey PRIMARY KEY (id)
);

CREATE TABLE public.business_rules (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name character varying NOT NULL,
  description text,
  rule_definition jsonb NOT NULL,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['validation'::character varying, 'workflow'::character varying, 'compliance'::character varying, 'security'::character varying, 'performance'::character varying]::text[])),
  scope character varying NOT NULL CHECK (scope::text = ANY (ARRAY['global'::character varying, 'module'::character varying, 'user'::character varying, 'rag_operations'::character varying]::text[])),
  applicable_modules text[] DEFAULT '{}'::text[],
  application_method character varying NOT NULL CHECK (application_method::text = ANY (ARRAY['pre_action'::character varying, 'post_action'::character varying, 'real_time'::character varying, 'scheduled'::character varying]::text[])),
  frequency character varying NOT NULL CHECK (frequency::text = ANY (ARRAY['always'::character varying, 'once'::character varying, 'daily'::character varying, 'weekly'::character varying, 'monthly'::character varying]::text[])),
  is_active boolean DEFAULT true,
  version integer DEFAULT 1 CHECK (version > 0),
  priority integer DEFAULT 100 CHECK (priority > 0),
  created_by uuid REFERENCES auth.users(id),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT business_rules_pkey PRIMARY KEY (id)
);

CREATE TABLE public.approval_workflows (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  organization_id uuid NOT NULL REFERENCES public.organizations(id),
  workflow_name character varying NOT NULL,
  workflow_type character varying NOT NULL CHECK (workflow_type::text = ANY (ARRAY['purchase_order'::character varying, 'expense'::character varying, 'contract'::character varying, 'invoice'::character varying, 'payment'::character varying, 'budget'::character varying]::text[])),
  is_active boolean DEFAULT true,
  requires_approval boolean DEFAULT true,
  approval_rules jsonb NOT NULL DEFAULT '{}'::jsonb,
  notification_settings jsonb DEFAULT '{}'::jsonb,
  steps jsonb NOT NULL DEFAULT '[]'::jsonb,
  escalation_rules jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid NOT NULL REFERENCES auth.users(id),
  updated_by uuid REFERENCES auth.users(id),
  description text,
  conditions jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT approval_workflows_pkey PRIMARY KEY (id)
);

CREATE TABLE public.audit_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  action character varying NOT NULL,
  resource_type character varying NOT NULL,
  resource_id uuid,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT audit_logs_pkey PRIMARY KEY (id)
);

CREATE TABLE public.user_management_audit (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  action character varying NOT NULL,
  resource_type character varying NOT NULL,
  resource_id uuid,
  old_values jsonb,
  new_values jsonb,
  ip_address inet,
  user_agent text,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_management_audit_pkey PRIMARY KEY (id)
);

CREATE TABLE public.rule_application_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  rule_id uuid NOT NULL REFERENCES public.business_rules(id),
  applied_at timestamp with time zone DEFAULT now(),
  context_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  result jsonb NOT NULL DEFAULT '{}'::jsonb,
  triggered_actions text[] DEFAULT '{}'::text[],
  entity_id uuid,
  entity_type character varying,
  user_id uuid REFERENCES auth.users(id),
  execution_time_ms integer DEFAULT 0 CHECK (execution_time_ms >= 0),
  success boolean NOT NULL,
  error_message text,
  module_id character varying,
  trigger_event character varying,
  rule_version integer DEFAULT 1 CHECK (rule_version > 0),
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT rule_application_logs_pkey PRIMARY KEY (id)
);

CREATE TABLE public.rag_query_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  query_id uuid NOT NULL,
  user_id uuid REFERENCES auth.users(id),
  query_text text NOT NULL,
  retrieval_parameters jsonb DEFAULT '{}'::jsonb,
  retrieved_documents jsonb DEFAULT '{}'::jsonb,
  generated_response text,
  latency_ms integer DEFAULT 0 CHECK (latency_ms >= 0),
  tokens_used_input integer DEFAULT 0 CHECK (tokens_used_input >= 0),
  tokens_used_output integer DEFAULT 0 CHECK (tokens_used_output >= 0),
  evaluation_score numeric CHECK (evaluation_score IS NULL OR evaluation_score >= 0::numeric AND evaluation_score <= 5::numeric),
  feedback jsonb DEFAULT '{}'::jsonb,
  errors jsonb DEFAULT '{}'::jsonb,
  business_rule_id uuid REFERENCES public.business_rules(id),
  applied_business_rules jsonb DEFAULT '{}'::jsonb,
  model_used character varying,
  temperature numeric CHECK (temperature IS NULL OR temperature >= 0::numeric AND temperature <= 2::numeric),
  max_tokens integer CHECK (max_tokens IS NULL OR max_tokens > 0),
  context_length integer CHECK (context_length IS NULL OR context_length > 0),
  success boolean NOT NULL DEFAULT true,
  query_type character varying DEFAULT 'standard'::character varying CHECK (query_type::text = ANY (ARRAY['standard'::character varying, 'follow_up'::character varying, 'clarification'::character varying, 'system'::character varying]::text[])),
  source_module character varying,
  session_id uuid,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT rag_query_logs_pkey PRIMARY KEY (id)
);

CREATE TABLE public.integration_configs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id),
  integration_type character varying NOT NULL CHECK (integration_type::text = ANY (ARRAY['woocommerce'::character varying, 'odoo'::character varying, 'shopify'::character varying, 'salesforce'::character varying, 'quickbooks'::character varying, 'stripe'::character varying, 'paypal'::character varying]::text[])),
  name character varying NOT NULL,
  config jsonb NOT NULL DEFAULT '{}'::jsonb,
  is_active boolean DEFAULT true,
  is_globally_enabled boolean DEFAULT true,
  last_known_status character varying DEFAULT 'unknown'::character varying CHECK (last_known_status::text = ANY (ARRAY['connected'::character varying, 'error'::character varying, 'unknown'::character varying, 'disabled'::character varying, 'pending'::character varying]::text[])),
  last_status_check_at timestamp with time zone,
  last_successful_sync_at timestamp with time zone,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  created_by uuid REFERENCES auth.users(id),
  CONSTRAINT integration_configs_pkey PRIMARY KEY (id)
);

CREATE TABLE public.integration_sync_settings (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  integration_id uuid NOT NULL REFERENCES public.integration_configs(id),
  entity_type character varying NOT NULL,
  is_enabled boolean DEFAULT true,
  sync_frequency character varying DEFAULT 'daily'::character varying CHECK (sync_frequency::text = ANY (ARRAY['real_time'::character varying, 'hourly'::character varying, 'daily'::character varying, 'weekly'::character varying, 'monthly'::character varying, 'manual'::character varying]::text[])),
  last_sync_at timestamp with time zone,
  sync_direction character varying DEFAULT 'bidirectional'::character varying CHECK (sync_direction::text = ANY (ARRAY['inbound'::character varying, 'outbound'::character varying, 'bidirectional'::character varying]::text[])),
  field_mappings jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT integration_sync_settings_pkey PRIMARY KEY (id)
);