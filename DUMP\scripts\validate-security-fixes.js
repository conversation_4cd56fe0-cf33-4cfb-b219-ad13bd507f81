#!/usr/bin/env node

/**
 * Simple validation script for database security fixes
 */

import fs from 'fs';
import path from 'path';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) { log(`✅ ${message}`, 'green'); }
function error(message) { log(`❌ ${message}`, 'red'); }
function warning(message) { log(`⚠️  ${message}`, 'yellow'); }
function info(message) { log(`ℹ️  ${message}`, 'blue'); }

// List of vulnerable functions that should be fixed
const VULNERABLE_FUNCTIONS = [
  'create_audit_trigger',
  'update_time_logs_updated_at',
  'increment_asset_download_count',
  'get_financial_dashboard_metrics',
  'update_stock_levels',
  'can_manage_brand',
  'check_credit_availability',
  'safe_apply_admin_fix',
  'create_complete_user',
  'update_user_preferences_updated_at',
  'update_rental_bookings_updated_at',
  'update_load_in_procedures_updated_at',
  'has_hr_permission'
];

function validateSecurityFixes() {
  info('🔍 Validating Database Security Fixes...');
  console.log('='.repeat(60));
  
  const migrationFile = 'supabase/migrations/20250128000006_fix_function_search_paths.sql';
  
  // Check if migration file exists
  if (!fs.existsSync(migrationFile)) {
    error(`Migration file not found: ${migrationFile}`);
    return false;
  }
  
  success(`Migration file found: ${migrationFile}`);
  
  // Read and analyze the migration file
  const sqlContent = fs.readFileSync(migrationFile, 'utf8');
  
  // Check for security patterns
  const securityChecks = [
    {
      pattern: /SET search_path = public, pg_temp/g,
      name: 'Immutable Search Path',
      required: true
    },
    {
      pattern: /SECURITY DEFINER/g,
      name: 'Security Definer',
      required: true
    },
    {
      pattern: /LANGUAGE plpgsql/g,
      name: 'Language Specification',
      required: true
    },
    {
      pattern: /CREATE OR REPLACE FUNCTION/g,
      name: 'Function Definitions',
      required: true
    },
    {
      pattern: /GRANT EXECUTE/g,
      name: 'Permission Grants',
      required: false
    }
  ];
  
  console.log('\n📋 Security Pattern Analysis:');
  console.log('-'.repeat(40));
  
  securityChecks.forEach(check => {
    const matches = (sqlContent.match(check.pattern) || []).length;
    if (matches > 0) {
      success(`${check.name}: ${matches} instances found`);
    } else if (check.required) {
      error(`${check.name}: Not found (REQUIRED)`);
    } else {
      warning(`${check.name}: Not found (optional)`);
    }
  });
  
  // Check individual functions
  console.log('\n🔧 Function Coverage Analysis:');
  console.log('-'.repeat(40));
  
  let fixedFunctions = [];
  let missingFunctions = [];
  
  VULNERABLE_FUNCTIONS.forEach(funcName => {
    const functionPattern = new RegExp(`CREATE OR REPLACE FUNCTION public\\.${funcName}`, 'i');
    const searchPathPattern = new RegExp(`CREATE OR REPLACE FUNCTION public\\.${funcName}[\\s\\S]*?SET search_path = public, pg_temp`, 'i');
    
    if (functionPattern.test(sqlContent)) {
      if (searchPathPattern.test(sqlContent)) {
        success(`${funcName} - Fixed with secure search path`);
        fixedFunctions.push(funcName);
      } else {
        warning(`${funcName} - Function found but missing secure search path`);
      }
    } else {
      error(`${funcName} - Function not found in migration`);
      missingFunctions.push(funcName);
    }
  });
  
  // Summary
  console.log('\n📊 Summary Report:');
  console.log('='.repeat(60));
  
  const totalFunctions = VULNERABLE_FUNCTIONS.length;
  const fixedCount = fixedFunctions.length;
  const fixRate = ((fixedCount / totalFunctions) * 100).toFixed(1);
  
  info(`Total Vulnerable Functions: ${totalFunctions}`);
  info(`Functions Fixed: ${fixedCount}`);
  info(`Fix Rate: ${fixRate}%`);
  
  if (fixedCount === totalFunctions) {
    success('🎉 All functions have been secured!');
  } else {
    warning(`${missingFunctions.length} functions still need attention`);
  }
  
  // File statistics
  const lines = sqlContent.split('\n').length;
  const size = fs.statSync(migrationFile).size;
  
  console.log('\n📄 Migration File Stats:');
  console.log('-'.repeat(40));
  info(`Lines: ${lines}`);
  info(`Size: ${size} bytes`);
  info(`Functions: ${(sqlContent.match(/CREATE OR REPLACE FUNCTION/g) || []).length}`);
  info(`Security Patterns: ${(sqlContent.match(/SET search_path/g) || []).length}`);
  
  // Next steps
  console.log('\n🚀 Next Steps:');
  console.log('-'.repeat(40));
  info('1. Apply the migration to your database');
  info('2. Run database linter to verify fixes');
  info('3. Test all affected functionality');
  info('4. Monitor for any issues');
  
  if (missingFunctions.length > 0) {
    console.log('\n⚠️  Functions needing attention:');
    missingFunctions.forEach(func => warning(`   - ${func}`));
  }
  
  return fixedCount === totalFunctions;
}

// Manual application instructions
function showManualInstructions() {
  console.log('\n📋 Manual Application Instructions:');
  console.log('='.repeat(60));
  
  info('Since Supabase CLI is not linked, apply the migration manually:');
  console.log('');
  info('Option 1 - Supabase Dashboard:');
  info('  1. Open your Supabase project dashboard');
  info('  2. Go to SQL Editor');
  info('  3. Copy the migration file contents');
  info('  4. Paste and execute the SQL');
  console.log('');
  info('Option 2 - Direct Database Connection:');
  info('  1. Connect to your PostgreSQL database');
  info('  2. Run: \\i supabase/migrations/20250128000006_fix_function_search_paths.sql');
  console.log('');
  info('Option 3 - Link Supabase Project:');
  info('  1. Run: supabase link --project-ref YOUR_PROJECT_REF');
  info('  2. Run: supabase db push');
}

// Main execution
console.log('🔒 Database Security Fix Validator');
console.log('='.repeat(60));

const isValid = validateSecurityFixes();

if (isValid) {
  success('\n✅ Security fixes validation PASSED');
} else {
  warning('\n⚠️  Security fixes validation INCOMPLETE');
}

showManualInstructions();

console.log('\n' + '='.repeat(60));
console.log('🔒 Security Fix Validation Complete');
