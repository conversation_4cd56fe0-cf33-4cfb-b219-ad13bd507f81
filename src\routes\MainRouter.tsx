import React from 'react';
import { Routes, Route, Navigate, Outlet } from 'react-router-dom';

import RouteGuard from '../components/auth/RouteGuard';



import ErrorBoundary from '@/components/error-handling/ErrorBoundary';
import MainLayout from '@/layouts/MainLayout';

// Simple test component to debug loading issues
const TestDashboard = () => {
  console.log('TestDashboard rendering...');
  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-4">🎉 Dashboard Loaded Successfully!</h1>
      <p className="text-gray-600 mb-4">If you can see this, the routing is working correctly.</p>
      <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <strong>Success!</strong> The application has loaded properly after login.
      </div>
      <div className="mt-4">
        <p className="text-sm text-gray-500">
          Timestamp: {new Date().toLocaleString()}
        </p>
      </div>
    </div>
  );
};

// Lazy load main components
const Dashboard = React.lazy(() => import('../pages/UnifiedDashboard'));
const Landing = React.lazy(() => import('../pages/SimpleLanding'));
const ResetPassword = React.lazy(() => import('../pages/ResetPassword'));
const Unauthorized = React.lazy(() => import('../pages/Unauthorized'));
const NotFound = React.lazy(() => import('../pages/NotFound'));

// Profile and account components  
const Profile = React.lazy(() => import('../pages/Profile'));
const AccountSecurity = React.lazy(() => import('../pages/AccountSecurity'));
const AccountBilling = React.lazy(() => import('../pages/AccountBilling'));
const Activity = React.lazy(() => import('../pages/Activity'));
const Help = React.lazy(() => import('../pages/Help'));
const Settings = React.lazy(() => import('../pages/Settings'));
const AdminDashboard = React.lazy(() => import('../pages/AdminDashboard'));

// Module route components
const DataManagementRoutes = React.lazy(() => import('./dataManagementRoutes'));
const CustomerManagementRoutes = React.lazy(() => import('./customerManagementRoutes'));
const BrandMarketingRoutes = React.lazy(() => import('./brandMarketingRoutes'));
const BrandManagementRoutes = React.lazy(() => import('./brandManagementRoutes'));
const MarketingRoutes = React.lazy(() => import('./marketingRoutes'));
const FinancialRoutes = React.lazy(() => import('./financialRoutes'));
const ProjectManagementRoutes = React.lazy(() => import('./projectManagementRoutes'));
const LoyaltyRoutes = React.lazy(() => import('./loyaltyRoutes'));

const TechHubRoutes = React.lazy(() => import('./techHubRoutesFixed'));
const DotXRoutes = React.lazy(() => import('./dotXRoutes'));
const RentalsEquipmentRoutes = React.lazy(() => import('./rentalsEquipmentRoutes'));
const TrainingEducationRoutes = React.lazy(() => import('./trainingEducationRoutes'));
const CoreSystemConfigRoutes = React.lazy(() => import('./coreSystemConfigRoutes').then(module => ({ default: module.CoreSystemConfigRoutes })));
const SupplyChainRoutes = React.lazy(() => import('./supplyChainRoutes'));
const DocuVaultRoutes = React.lazy(() => import('./docuVaultRoutes').then(module => ({ default: module.DocuVaultRoutes })));
const EventProductionRoutes = React.lazy(() => import('./eventProductionRoutes').then(module => ({ default: module.EventProductionRoutes })));
const WorkshopRepairsRoutes = React.lazy(() => import('./workshopRepairsRoutes').then(module => ({ default: module.WorkshopRepairsRoutes })));
const BusinessGovernanceRoutes = React.lazy(() => import('./businessGovernanceRoutes').then(module => ({ default: module.BusinessGovernanceRoutes })));
const HrRoutes = React.lazy(() => import('./hrRoutes').then(module => ({ default: module.HRRoutes })));
const EventsRoutes = React.lazy(() => import('./eventsRoutes'));

const CustomerHubRoutes = React.lazy(() => import('./customerHubRoutes').then(module => ({ default: module.CustomerHubRoutes })));
const AdminRoutes = React.lazy(() => import('./adminRoutesFixed'));

// Error fallback component
const RouteFallback = () => (
  <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
    <h2 className="text-xl font-bold text-red-800 mb-4">Route Error</h2>
    <p className="mb-4">There was an error loading this route.</p>
    <div className="flex justify-between">
      <a href="/" className="text-blue-600 hover:underline">Return to Dashboard</a>
      <button 
        onClick={() => globalThis.location.reload()} 
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        Refresh Page
      </button>
    </div>
  </div>
);

// Loading component
const LoadingComponent = () => (
  <div className="flex justify-center items-center h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
      <p className="mt-4 text-gray-600">Loading QuantaSori Platform...</p>
    </div>
  </div>
);

/**
 * Complete QuantaSori V2.0 Platform Router
 * Handles all modules and routing for the unified platform
 */
const MainRouter: React.FC = () => {
  console.log('MainRouter rendering with full QuantaSori platform...');
  
  return (
    <ErrorBoundary fallback={<RouteFallback />}>
      <React.Suspense fallback={<LoadingComponent />}>
        <Routes>
          {/* Public routes - no layout */}
          {/* Wrap routes with enhanced error boundaries */}
          <Route path="/" element={<ErrorBoundary><Landing /></ErrorBoundary>} />
          <Route path="/landing" element={<Landing />} />
          <Route path="/reset-password" element={<ErrorBoundary><ResetPassword /></ErrorBoundary>} />
          <Route path="/unauthorized" element={<Unauthorized />} />

          {/* All authenticated routes wrapped in MainLayout */}
          <Route
            path="/dashboard"
            element={
              <RouteGuard fallbackPath="/">
                <MainLayout />
              </RouteGuard>
            }
          >
            {/* Main Dashboard */}
            <Route index element={<Dashboard />} />
          
            {/* Profile and account routes */}
            <Route path="profile" element={<Profile />} />
            <Route path="account-security" element={<AccountSecurity />} />
            <Route path="account-billing" element={<AccountBilling />} />
            <Route path="activity" element={<Activity />} />
            <Route path="help" element={<Help />} />
            <Route path="settings" element={<Settings />} />

            {/* Module Routes - All standardized to /dashboard/module-name/* pattern */}
            <Route path="data-management/*" element={<DataManagementRoutes />} />
            <Route path="customer-management/*" element={<CustomerManagementRoutes />} />
            <Route path="customer-hub/*" element={<CustomerHubRoutes />} />
            <Route path="brand-marketing/*" element={<BrandMarketingRoutes />} />
            <Route path="brand-management/*" element={<BrandManagementRoutes />} />
            <Route path="marketing/*" element={<MarketingRoutes />} />
            <Route path="project-management/*" element={<ProjectManagementRoutes />} />
            <Route path="financial/*" element={<FinancialRoutes />} />
            <Route path="supply-chain/*" element={<SupplyChainRoutes />} />
            <Route path="loyalty-rewards/*" element={<LoyaltyRoutes />} />

            <Route path="tech-hub/*" element={<TechHubRoutes />} />
            <Route path="dot-x/*" element={<DotXRoutes />} />
            <Route path="dotx/*" element={<DotXRoutes />} />

            <Route path="rentals-equipment/*" element={<RentalsEquipmentRoutes />} />
            <Route path="training-education/*" element={<TrainingEducationRoutes />} />
            <Route path="core-system-config/*" element={<CoreSystemConfigRoutes />} />
            <Route path="docuvault/*" element={<DocuVaultRoutes />} />
            <Route path="event-production/*" element={<EventProductionRoutes />} />
            <Route path="workshop-repairs/*" element={<WorkshopRepairsRoutes />} />
            <Route path="business-governance/*" element={<BusinessGovernanceRoutes />} />
            <Route path="hr-people/*" element={<HrRoutes />} />
            <Route path="events/*" element={<EventsRoutes />} />
           
            {/* Admin routes */}
            <Route
              path="admin/*"
              element={
                <RouteGuard fallbackPath="/landing" requiredRole={'admin' as any}>
                  <AdminRoutes />
                </RouteGuard>
              }
            />

            {/* Redirects and legacy routes */}
            {/* Ensure valid old route handling or consolidation */}
            <Route path="dashboard" element={<Navigate to="/dashboard" replace />} />
          </Route>
          
          {/* 404 - Not Found */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default MainRouter;
