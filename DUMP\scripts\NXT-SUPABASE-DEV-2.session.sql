SELECT
    p.polname AS policy_name,
    c.relname AS table_name,
    CASE
        WHEN p.polcmd = 'r' THEN 'SELECT'
        WHEN p.polcmd = 'a' THEN 'INSERT'
        WHEN p.polcmd = 'w' THEN 'UPDATE'
        WHEN p.polcmd = 'd' THEN 'DELETE'
    END AS command,
    pg_get_expr(p.polqual, c.oid) AS using_expression,
    pg_get_expr(p.polwithcheck, c.oid) AS with_check_expression,
    r.rolname AS role_name
FROM
    pg_policy p
JOIN
    pg_class c ON c.oid = p.polrelid
JOIN
    pg_roles r ON r.oid = ANY(p.polroles)
WHERE
    c.relname = 'profiles';