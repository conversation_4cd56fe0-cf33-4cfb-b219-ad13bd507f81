import {
  Home,
  Server,
  Database,
  Settings,
  Users,
  Truck,
  UserCog,
  FileArchive,
  BarChart3,
  Award,
  Gift,
  FileText,
  AlertTriangle,
  BrainCircuit,
  BookOpen,
  Globe,
  FileUp,
  Calculator,
  LineChart,
  Palette,
  Lock,
  Package,
  Activity,
  FolderTree,
  Upload,
  Calendar,
  Megaphone,
  Wrench,
  ShoppingCart,
  Building2,
  Zap,
  Plus,
  Clock,
  Bell
} from 'lucide-react';
import { NavItem, NavCategory } from './types';

// Main sidebar configuration matching the required structure

export const topLevelNavItems: NavItem[] = [
  { label: 'Main Dashboard', icon: Home, href: '/', path: '/' }
];

export const navCategories: NavCategory[] = [
  {
    label: 'TECHNOLOGY MANAGEMENT',
    name: 'TECHNOLOGY MANAGEMENT',
    items: [
      { label: 'Tech Hub', icon: Server, href: '/dashboard/tech-hub', path: '/dashboard/tech-hub' },
      { label: 'Data Management', icon: Database, href: '/dashboard/data-management', path: '/dashboard/data-management' },
      { label: 'Core System Configuration', icon: Settings, href: '/dashboard/core-system-config', path: '/dashboard/core-system-config' }
    ]
  },
  {
    label: 'ADMIN PANEL',
    name: 'ADMIN PANEL',
    items: [
      { label: 'Admin Dashboard', icon: Settings, href: '/dashboard/admin', path: '/dashboard/admin' },
      { label: 'User Management', icon: Users, href: '/dashboard/admin/users', path: '/dashboard/admin/users' },
      { label: 'Roles & Permissions', icon: UserCog, href: '/dashboard/admin/roles-permissions', path: '/dashboard/admin/roles-permissions' },
      { label: 'System Settings', icon: Settings, href: '/dashboard/admin/settings', path: '/dashboard/admin/settings' },
      { label: 'Database Admin', icon: Database, href: '/dashboard/admin/database', path: '/dashboard/admin/database' },
      { label: 'Security Center', icon: Lock, href: '/dashboard/admin/security', path: '/dashboard/admin/security' },
      { label: 'Documentation', icon: FileText, href: '/dashboard/admin/documentation', path: '/dashboard/admin/documentation' },
      { label: 'Module Access', icon: Server, href: '/dashboard/admin/module-access', path: '/dashboard/admin/module-access' }
    ]
  },
  {
    label: 'SUPPLY CHAIN MANAGEMENT',
    name: 'SUPPLY CHAIN MANAGEMENT',
    items: [
      { label: 'Supply Chain Dashboard', icon: Truck, href: '/dashboard/supply-chain', path: '/dashboard/supply-chain' },
      { label: 'Purchase Orders', icon: FileText, href: '/dashboard/supply-chain/purchase-orders', path: '/dashboard/supply-chain/purchase-orders' },
      { label: 'Goods Received Notes', icon: Package, href: '/dashboard/supply-chain/goods-received', path: '/dashboard/supply-chain/goods-received' },
      { label: 'Supplier Management', icon: Users, href: '/dashboard/supply-chain/supplier-management', path: '/dashboard/supply-chain/supplier-management' },
      { label: 'Vendor Management', icon: UserCog, href: '/dashboard/supply-chain/vendor-management', path: '/dashboard/supply-chain/vendor-management' },
      { label: 'Inventory Stock', icon: FileArchive, href: '/dashboard/supply-chain/inventory-stock', path: '/dashboard/supply-chain/inventory-stock' },
      { label: 'Stock Movements', icon: Activity, href: '/dashboard/supply-chain/stock-movements', path: '/dashboard/supply-chain/stock-movements' },
      { label: 'Supplier Costing', icon: Calculator, href: '/dashboard/supply-chain/supplier-costing', path: '/dashboard/supply-chain/supplier-costing' },
      { label: 'Cost Analysis', icon: BarChart3, href: '/dashboard/supply-chain/costing', path: '/dashboard/supply-chain/costing' },
      { label: 'Category Management', icon: FolderTree, href: '/dashboard/supply-chain/category-management', path: '/dashboard/supply-chain/category-management' },
      { label: 'Uploads & Data', icon: Upload, href: '/dashboard/supply-chain/uploads', path: '/dashboard/supply-chain/uploads' }
    ]
  },
  {
    label: 'BUSINESS MODULES',
    name: 'BUSINESS MODULES',
    items: [
      { label: 'Financial & Accounting', icon: BarChart3, href: '/dashboard/financial', path: '/dashboard/financial' },
      {
        label: 'Events Management',
        icon: Calendar,
        href: '/dashboard/events',
        path: '/dashboard/events',
        children: [
          { label: 'Dashboard', icon: Calendar, href: '/dashboard/events', path: '/dashboard/events' },
          { label: 'Calendar', icon: Calendar, href: '/dashboard/events/calendar', path: '/dashboard/events/calendar' },
          { label: 'Create Event', icon: Plus, href: '/dashboard/events/new', path: '/dashboard/events/new' },
          { label: 'Schedule', icon: Clock, href: '/dashboard/events/schedule', path: '/dashboard/events/schedule' },
          { label: 'Notifications', icon: Bell, href: '/dashboard/events/notifications', path: '/dashboard/events/notifications' }
        ]
      },
      { label: 'Customer Hub', icon: Users, href: '/dashboard/customer-hub', path: '/dashboard/customer-hub' },
      { label: 'Loyalty & Rewards', icon: Award, href: '/dashboard/loyalty-rewards', path: '/dashboard/loyalty-rewards' },
      { label: 'DocuVault', icon: FileText, href: '/dashboard/docuvault', path: '/dashboard/docuvault' },
      { label: 'Project Management', icon: BookOpen, href: '/dashboard/project-management', path: '/dashboard/project-management' },
      { label: 'Rentals & Equipment Hire', icon: Wrench, href: '/dashboard/rentals-equipment', path: '/dashboard/rentals-equipment' },
      { label: 'Event Production', icon: Calendar, href: '/dashboard/event-production', path: '/dashboard/event-production' },
      { label: 'Workshop & Repairs', icon: Settings, href: '/dashboard/workshop-repairs', path: '/dashboard/workshop-repairs' },
      { label: 'Training & Education', icon: BookOpen, href: '/dashboard/training-education', path: '/dashboard/training-education' },
      { label: 'Business Governance', icon: Building2, href: '/dashboard/business-governance', path: '/dashboard/business-governance' },
      { label: 'HR & People Management', icon: Users, href: '/dashboard/hr-people', path: '/dashboard/hr-people' },
      { label: 'Marketing & Social Media', icon: Megaphone, href: '/dashboard/marketing', path: '/dashboard/marketing' },
      { label: 'Brand Management', icon: Palette, href: '/dashboard/brand-management', path: '/dashboard/brand-management' }
    ]
  },
  {
    label: 'INTEGRATIONS',
    name: 'INTEGRATIONS',
    items: [
      { label: 'WooCommerce', icon: ShoppingCart, href: '/dashboard/customer-management', path: '/dashboard/customer-management' },
      { label: 'Odoo ERP', icon: Building2, href: '/dashboard/financial', path: '/dashboard/financial' },
      { label: 'Tech Hub', icon: Zap, href: '/dashboard/tech-hub', path: '/dashboard/tech-hub' }
    ]
  },

  {
    label: 'ADMINISTRATION',
    name: 'ADMINISTRATION',
    items: [
      { label: 'User Management', icon: Users, href: '/dashboard/admin/users', path: '/dashboard/admin/users' },
      { label: 'Database Admin', icon: Database, href: '/dashboard/admin/database', path: '/dashboard/admin/database' }
    ]
  },

];

// No secondary sidebar, all items consolidated above.
