// Local RBAC permissions module with comprehensive permission set
export enum Permission {
  // System permissions
  SYSTEM_ADMIN = 'system:admin',
  USER_MANAGEMENT = 'user:management',
  MODULE_ACCESS_VIEW = 'module:access:view',
  
  // User Management permissions
  USER_MANAGEMENT_VIEW = 'user_management:view',
  USER_MANAGEMENT_CREATE = 'user_management:create',
  USER_MANAGEMENT_EDIT = 'user_management:edit',
  USER_MANAGEMENT_DELETE = 'user_management:delete',
  USER_MANAGEMENT_ADMIN = 'user_management:admin',
  
  // Tech Hub permissions
  TECH_HUB_ACCESS = 'tech_hub:access',
  TECH_HUB_ADMIN = 'tech_hub:admin',
  
  // Product permissions
  PRODUCT_VIEW = 'product:view',
  PRODUCT_EDIT = 'product:edit',
  
  // File permissions
  FILE_VIEW = 'file:view',
  FILE_UPLOAD = 'file:upload',
  FILE_DELETE = 'file:delete',
  FILES = 'files:access',
  
  // Communication permissions
  COMMUNICATION_ACCESS = 'communication:access',
  COMMUNICATION_EDIT = 'communication:edit',
  
  // Social Media permissions
  SOCIAL_MEDIA_ACCESS = 'social_media:access',
  SOCIAL_MEDIA_EDIT = 'social_media:edit',
  
  // Supplier Management permissions
  SUPPLIER_MANAGEMENT = 'supplier:management',
  SUPPLIER_VIEW = 'supplier:view',
  SUPPLIER_EDIT = 'supplier:edit',
  
  // Role Management permissions
  ROLE_MANAGEMENT_VIEW = 'role_management:view',
  ROLE_MANAGEMENT_EDIT = 'role_management:edit',
  ROLE_MANAGEMENT_ADMIN = 'role_management:admin',
  
  PERMISSION_MANAGEMENT_VIEW = 'permission_management:view',
  PERMISSION_MANAGEMENT_EDIT = 'permission_management:edit',
  PERMISSION_MANAGEMENT_ADMIN = 'permission_management:admin',
  
  VIEW_DASHBOARD = 'view:dashboard',
  
  FINANCIAL_VIEW = 'financial:view',
  FINANCIAL_EDIT = 'financial:edit',
  FINANCIAL_DELETE = 'financial:delete',
  FINANCIAL_ADMIN = 'financial:admin',
  FINANCIAL_ACCESS = 'financial:access',
  
  CUSTOMER_MANAGEMENT = 'customer:management',
  CUSTOMER_VIEW = 'customer:view',
  CUSTOMER_EDIT = 'customer:edit',
  CUSTOMER_PROFILE_MANAGE = 'customer:profile:manage',
  
  // Existing permissions remain the same
  PROJECT_MANAGEMENT = 'project:management',
  PROJECT_VIEW = 'project:view',
  PROJECT_EDIT = 'project:edit',
  
  DATA_MANAGEMENT = 'data:management',
  DATA_MANAGEMENT_ACCESS = 'data:access',
  DATA_MANAGEMENT_EDIT = 'data:edit',

  // Events Management permissions
  EVENTS_VIEW = 'events:view',
  EVENTS_EDIT = 'events:edit',
  EVENTS_CREATE = 'events:create',
  EVENTS_DELETE = 'events:delete',
  EVENTS_ADMIN = 'events:admin',
  EVENTS_MANAGEMENT = 'events:management'
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user',
  GUEST = 'guest'
}

export interface Role {
  name: string;
  permissions: Permission[];
}

export const ROLES: Record<string, Role> = {
  SUPER_ADMIN: {
    name: 'Super Administrator',
    permissions: Object.values(Permission),
  },
  ADMIN: {
    name: 'Administrator',
    permissions: Object.values(Permission),
  },
  MANAGER: {
    name: 'Manager',
    permissions: [
      Permission.VIEW_DASHBOARD,
      Permission.USER_MANAGEMENT_VIEW,
      Permission.TECH_HUB_ACCESS,
      Permission.PRODUCT_VIEW,
      Permission.FILE_VIEW,
      Permission.FILES,
      Permission.COMMUNICATION_ACCESS,
      Permission.SOCIAL_MEDIA_ACCESS,
      Permission.SUPPLIER_VIEW,
      Permission.CUSTOMER_VIEW,
      Permission.PROJECT_VIEW,
      Permission.FINANCIAL_VIEW,
      Permission.EVENTS_VIEW,
      Permission.EVENTS_EDIT,
      Permission.EVENTS_CREATE
    ],
  },
  USER: {
    name: 'User',
    permissions: [
      Permission.VIEW_DASHBOARD,
      Permission.TECH_HUB_ACCESS,
      Permission.PRODUCT_VIEW,
      Permission.FILE_VIEW,
      Permission.FILES,
      Permission.COMMUNICATION_ACCESS,
      Permission.SOCIAL_MEDIA_ACCESS,
      Permission.CUSTOMER_VIEW,
      Permission.PROJECT_VIEW,
      Permission.EVENTS_VIEW
    ],
  },
  GUEST: {
    name: 'Guest',
    permissions: [
      Permission.VIEW_DASHBOARD
    ],
  }
}

export function hasPermission(userRole: UserRole, requiredPermission: Permission): boolean {
  const role = ROLES[userRole.toUpperCase()];
  if (!role) return false;
  
  return role.permissions.includes(requiredPermission);
}

export function hasAnyPermission(userRole: UserRole, requiredPermissions: Permission[]): boolean {
  return requiredPermissions.some(permission => hasPermission(userRole, permission));
}

export function hasAllPermissions(userRole: UserRole, requiredPermissions: Permission[]): boolean {
  return requiredPermissions.every(permission => hasPermission(userRole, permission));
}

export function isRoleOrHigher(currentRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy = {
    [UserRole.GUEST]: 0,
    [UserRole.USER]: 1,
    [UserRole.MANAGER]: 2,
    [UserRole.ADMIN]: 3,
    [UserRole.SUPER_ADMIN]: 4
  };
  
  return roleHierarchy[currentRole] >= roleHierarchy[requiredRole];
}