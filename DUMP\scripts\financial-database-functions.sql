-- ================================================================================================
-- FINANCIAL SYSTEM DATABASE FUNCTIONS
-- Version: 2.0 - Production Ready
-- Description: Comprehensive stored procedures and functions for financial operations
-- ================================================================================================

-- Section 1: Utility Functions
-- ================================================================================================

-- Function to generate unique account codes
CREATE OR REPLACE FUNCTION generate_account_code(
    p_organization_id UUID,
    p_account_type TEXT
) RETURNS TEXT AS $$
DECLARE
    v_prefix TEXT;
    v_next_number INTEGER;
    v_account_code TEXT;
BEGIN
    -- Determine prefix based on account type
    CASE p_account_type
        WHEN 'Asset' THEN v_prefix := '1';
        WHEN 'Liability' THEN v_prefix := '2';
        WHEN 'Equity' THEN v_prefix := '3';
        WHEN 'Revenue' THEN v_prefix := '4';
        WHEN 'Expense' THEN v_prefix := '5';
        ELSE v_prefix := '9';
    END CASE;
    
    -- Get next available number for this type
    SELECT COALESCE(MAX(CAST(SUBSTRING(account_code FROM 2) AS INTEGER)), 0) + 1
    INTO v_next_number
    FROM chart_of_accounts
    WHERE organization_id = p_organization_id
    AND account_code LIKE v_prefix || '%';
    
    -- Format as 4-digit code
    v_account_code := v_prefix || LPAD(v_next_number::TEXT, 3, '0');
    
    RETURN v_account_code;
END;
$$ LANGUAGE plpgsql;

-- Function to generate journal entry numbers
CREATE OR REPLACE FUNCTION generate_journal_entry_number(
    p_organization_id UUID
) RETURNS TEXT AS $$
DECLARE
    v_year TEXT;
    v_next_number INTEGER;
    v_entry_number TEXT;
BEGIN
    v_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 6) AS INTEGER)), 0) + 1
    INTO v_next_number
    FROM journal_entries
    WHERE organization_id = p_organization_id
    AND entry_number LIKE 'JE' || v_year || '%';
    
    v_entry_number := 'JE' || v_year || LPAD(v_next_number::TEXT, 4, '0');
    
    RETURN v_entry_number;
END;
$$ LANGUAGE plpgsql;

-- Function to generate payment numbers
CREATE OR REPLACE FUNCTION generate_payment_number(
    p_organization_id UUID
) RETURNS TEXT AS $$
DECLARE
    v_year TEXT;
    v_next_number INTEGER;
    v_payment_number TEXT;
BEGIN
    v_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(payment_number FROM 4) AS INTEGER)), 0) + 1
    INTO v_next_number
    FROM payments
    WHERE organization_id = p_organization_id
    AND payment_number LIKE 'PAY' || v_year || '%';
    
    v_payment_number := 'PAY' || v_year || LPAD(v_next_number::TEXT, 4, '0');
    
    RETURN v_payment_number;
END;
$$ LANGUAGE plpgsql;

-- Section 2: Financial Calculation Functions
-- ================================================================================================

-- Function to calculate account balance
CREATE OR REPLACE FUNCTION get_account_balance(
    p_account_id UUID,
    p_as_of_date DATE DEFAULT CURRENT_DATE
) RETURNS NUMERIC AS $$
DECLARE
    v_balance NUMERIC := 0;
    v_account_type TEXT;
BEGIN
    -- Get account type
    SELECT account_type INTO v_account_type
    FROM chart_of_accounts
    WHERE id = p_account_id;
    
    -- Calculate balance based on account type
    SELECT 
        CASE 
            WHEN v_account_type IN ('Asset', 'Expense') THEN
                COALESCE(SUM(debit_amount - credit_amount), 0)
            ELSE
                COALESCE(SUM(credit_amount - debit_amount), 0)
        END
    INTO v_balance
    FROM general_ledger
    WHERE account_id = p_account_id
    AND transaction_date <= p_as_of_date;
    
    RETURN v_balance;
END;
$$ LANGUAGE plpgsql;

-- Function to validate journal entry balance
CREATE OR REPLACE FUNCTION validate_journal_entry_balance(
    p_journal_entry_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_total_debits NUMERIC;
    v_total_credits NUMERIC;
BEGIN
    SELECT 
        COALESCE(SUM(debit_amount), 0),
        COALESCE(SUM(credit_amount), 0)
    INTO v_total_debits, v_total_credits
    FROM journal_entry_lines
    WHERE journal_entry_id = p_journal_entry_id;
    
    RETURN v_total_debits = v_total_credits;
END;
$$ LANGUAGE plpgsql;

-- Section 3: Financial Reporting Functions
-- ================================================================================================

-- Function to get trial balance
CREATE OR REPLACE FUNCTION get_trial_balance(
    p_organization_id UUID,
    p_as_of_date DATE DEFAULT CURRENT_DATE
) RETURNS TABLE (
    account_code TEXT,
    account_name TEXT,
    account_type TEXT,
    debit_balance NUMERIC,
    credit_balance NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        coa.account_code,
        coa.account_name,
        coa.account_type,
        CASE 
            WHEN coa.account_type IN ('Asset', 'Expense') THEN
                GREATEST(COALESCE(SUM(gl.debit_amount - gl.credit_amount), 0), 0)
            ELSE 0
        END as debit_balance,
        CASE 
            WHEN coa.account_type IN ('Liability', 'Equity', 'Revenue') THEN
                GREATEST(COALESCE(SUM(gl.credit_amount - gl.debit_amount), 0), 0)
            ELSE 0
        END as credit_balance
    FROM chart_of_accounts coa
    LEFT JOIN general_ledger gl ON coa.id = gl.account_id 
        AND gl.transaction_date <= p_as_of_date
    WHERE coa.organization_id = p_organization_id
    AND coa.is_active = true
    GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
    ORDER BY coa.account_code;
END;
$$ LANGUAGE plpgsql;

-- Function to get financial dashboard metrics
CREATE OR REPLACE FUNCTION get_financial_dashboard_metrics(
    p_organization_id UUID
) RETURNS TABLE (
    total_assets NUMERIC,
    total_liabilities NUMERIC,
    net_worth NUMERIC,
    monthly_revenue NUMERIC,
    monthly_expenses NUMERIC,
    net_income NUMERIC,
    outstanding_receivables NUMERIC,
    outstanding_payables NUMERIC,
    cash_balance NUMERIC,
    overdue_invoices INTEGER
) AS $$
DECLARE
    v_current_month_start DATE;
    v_current_month_end DATE;
BEGIN
    v_current_month_start := DATE_TRUNC('month', CURRENT_DATE);
    v_current_month_end := v_current_month_start + INTERVAL '1 month' - INTERVAL '1 day';
    
    RETURN QUERY
    SELECT 
        -- Total Assets
        COALESCE((
            SELECT SUM(get_account_balance(id))
            FROM chart_of_accounts
            WHERE organization_id = p_organization_id
            AND account_type = 'Asset'
            AND is_active = true
        ), 0) as total_assets,
        
        -- Total Liabilities
        COALESCE((
            SELECT SUM(get_account_balance(id))
            FROM chart_of_accounts
            WHERE organization_id = p_organization_id
            AND account_type = 'Liability'
            AND is_active = true
        ), 0) as total_liabilities,
        
        -- Net Worth (Assets - Liabilities)
        COALESCE((
            SELECT SUM(
                CASE 
                    WHEN account_type = 'Asset' THEN get_account_balance(id)
                    WHEN account_type = 'Liability' THEN -get_account_balance(id)
                    ELSE 0
                END
            )
            FROM chart_of_accounts
            WHERE organization_id = p_organization_id
            AND account_type IN ('Asset', 'Liability')
            AND is_active = true
        ), 0) as net_worth,
        
        -- Monthly Revenue
        COALESCE((
            SELECT SUM(credit_amount - debit_amount)
            FROM general_ledger gl
            JOIN chart_of_accounts coa ON gl.account_id = coa.id
            WHERE coa.organization_id = p_organization_id
            AND coa.account_type = 'Revenue'
            AND gl.transaction_date >= v_current_month_start
            AND gl.transaction_date <= v_current_month_end
        ), 0) as monthly_revenue,
        
        -- Monthly Expenses
        COALESCE((
            SELECT SUM(debit_amount - credit_amount)
            FROM general_ledger gl
            JOIN chart_of_accounts coa ON gl.account_id = coa.id
            WHERE coa.organization_id = p_organization_id
            AND coa.account_type = 'Expense'
            AND gl.transaction_date >= v_current_month_start
            AND gl.transaction_date <= v_current_month_end
        ), 0) as monthly_expenses,
        
        -- Net Income (Revenue - Expenses for current month)
        COALESCE((
            SELECT SUM(
                CASE 
                    WHEN coa.account_type = 'Revenue' THEN credit_amount - debit_amount
                    WHEN coa.account_type = 'Expense' THEN -(debit_amount - credit_amount)
                    ELSE 0
                END
            )
            FROM general_ledger gl
            JOIN chart_of_accounts coa ON gl.account_id = coa.id
            WHERE coa.organization_id = p_organization_id
            AND coa.account_type IN ('Revenue', 'Expense')
            AND gl.transaction_date >= v_current_month_start
            AND gl.transaction_date <= v_current_month_end
        ), 0) as net_income,
        
        -- Outstanding Receivables
        COALESCE((
            SELECT SUM(amount_due)
            FROM accounts_receivable
            WHERE organization_id = p_organization_id
            AND status IN ('Open', 'Overdue')
        ), 0) as outstanding_receivables,
        
        -- Outstanding Payables
        COALESCE((
            SELECT SUM(amount_due)
            FROM accounts_payable
            WHERE organization_id = p_organization_id
            AND status IN ('Open', 'Overdue')
        ), 0) as outstanding_payables,
        
        -- Cash Balance
        COALESCE((
            SELECT SUM(current_balance)
            FROM bank_accounts
            WHERE organization_id = p_organization_id
            AND is_active = true
        ), 0) as cash_balance,
        
        -- Overdue Invoices Count
        COALESCE((
            SELECT COUNT(*)::INTEGER
            FROM accounts_receivable
            WHERE organization_id = p_organization_id
            AND status = 'Overdue'
        ), 0) as overdue_invoices;
END;
$$ LANGUAGE plpgsql;
